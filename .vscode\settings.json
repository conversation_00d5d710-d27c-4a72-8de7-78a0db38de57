{
    "files.exclude": {
      "**/venv": true,
      "**/work_data": true,
      "**/.idea": true,
      "**/__pycache__": true,
      "**/.pytest_cache": true,
      "**/*.pyc": true  // 可选：隐藏所有.pyc编译文件
    },
    // 通用配置（适用于所有操作系统）
    "python.analysis.extraPaths": ["${workspaceFolder}"],  // 静态分析时额外搜索路径[2](@ref)
    
    // 按操作系统设置终端环境变量
    "terminal.integrated.env.windows": {
      "PYTHONPATH": "${workspaceFolder};${env:PYTHONPATH}"  // Windows[1,3](@ref)
    },
  }