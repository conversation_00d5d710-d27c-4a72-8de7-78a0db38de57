import base64
from PIL import Image
from io import BytesIO
import re
import os


def save_base64_image(base64_string, output_file=None):
    """
    将 Base64 编码的图片字符串保存为图片文件，自动识别格式并设置扩展名。

    :param base64_string: Base64 编码的图片字符串（如 "data:image/png;base64,..."）
    :param output_file: 输出文件路径（可选）。如果未提供，则自动生成带正确扩展名的文件名。
    :return: 保存的文件路径（方便后续使用）
    """
    # 1. 提取 MIME 类型和 Base64 数据
    pattern = r"data:image/(\w+);base64,(.*)"
    match = re.fullmatch(pattern, base64_string.strip())

    if not match:
        raise ValueError("Invalid base64 image string format. Expected format: 'data:image/[format];base64,[data]'")

    image_format = match.group(1).lower()  # 如 "png", "jpeg", "gif" 等
    base64_data = match.group(2)

    # 2. 解码 Base64 字符串
    try:
        image_bytes = base64.b64decode(base64_data)
    except Exception as e:
        raise ValueError("Failed to decode Base64 data. Invalid or corrupted data.") from e

    # 3. 将二进制数据转换为图像
    try:
        image = Image.open(BytesIO(image_bytes))
    except Exception as e:
        raise ValueError("Failed to parse image data. Unsupported or corrupted image.") from e

    # 4. 确定输出文件名（如果未提供，则自动生成）
    if output_file is None:
        # 默认文件名（如 "output.png"）
        output_file = f"output.{image_format}"
    else:
        # 如果用户提供了文件名，但未带扩展名，则自动添加
        base_name, ext = os.path.splitext(output_file)
        if not ext:
            output_file = f"{output_file}.{image_format}"
        else:
            # 如果用户提供了扩展名，但与 MIME 类型不匹配，则自动修正
            user_ext = ext[1:].lower()  # 去掉点，如 "png"
            if user_ext != image_format:
                print(
                    f"Warning: Output file extension '{user_ext}' does not match detected format '{image_format}'. Auto-correcting to '{image_format}'.")
                output_file = f"{base_name}.{image_format}"

    # 5. 保存图片
    try:
        image.save(output_file, format=image_format)
        print(f"图片已保存为 {output_file} (格式: {image_format})")
        return output_file  # 返回文件路径，方便后续使用
    except Exception as e:
        raise ValueError(f"Failed to save image. Unsupported format or other error: {e}") from e


def extract_multiple_base64_images(text):
    """
    从文本中提取所有符合 `data:image/(\w+);base64,([a-zA-Z0-9+/=]+)` 格式的 Base64 图片数据。

    :param text: 包含多个 Base64 图片数据的文本
    :return: 提取到的 Base64 图片字符串列表
    """
    pattern = r"data:image/(\w+);base64,([a-zA-Z0-9+/=]+)"
    matches = re.findall(pattern, text)

    if not matches:
        raise ValueError("未找到任何符合格式的 Base64 图片数据。")

    # 重构完整的 Base64 图片字符串（带前缀）
    base64_image_strings = [
        f"data:image/{mime_type};base64,{base64_data}"
        for mime_type, base64_data in matches
    ]

    return base64_image_strings


def convert_inline_images_to_local(file_path, output_dir=None, image_prefix="img"):
    """
    将文件中的内联图片（data:image格式）转换为本地图片文件，并更新文件内容中的链接。

    :param file_path: 输入文件路径
    :param output_dir: 图片输出目录（可选）。如果未提供，则在文件同目录下创建images文件夹
    :param image_prefix: 图片文件名前缀（可选），默认为"img"
    :return: 更新后的文件内容字符串
    """
    # 1. 读取文件内容
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        raise ValueError(f"无法读取文件 {file_path}: {e}") from e

    # 2. 提取所有内联图片
    try:
        base64_images = extract_multiple_base64_images(content)
    except ValueError:
        # 如果没有找到图片，直接返回原内容
        print(f"文件 {file_path} 中未找到内联图片")
        return content

    # 3. 确定输出目录
    if output_dir is None:
        file_dir = os.path.dirname(file_path)
        output_dir = os.path.join(file_dir, "images")
    
    # 4. 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)

    # 5. 处理每个内联图片
    pattern = r"data:image/(\w+);base64,([a-zA-Z0-9+/=]+)"
    
    def replace_image(match):
        mime_type = match.group(1)
        base64_data = match.group(2)
        
        # 生成唯一的图片文件名
        import hashlib
        import time
        timestamp = int(time.time() * 1000)
        hash_suffix = hashlib.md5(base64_data.encode()).hexdigest()[:8]
        image_filename = f"{image_prefix}_{timestamp}_{hash_suffix}.{mime_type}"
        image_path = os.path.join(output_dir, image_filename)
        
        # 保存图片
        full_base64_string = f"data:image/{mime_type};base64,{base64_data}"
        try:
            save_base64_image(full_base64_string, image_path)
            print(f"已保存图片: {image_path}")
            
            # 返回HTML格式的img标签，直接使用output_dir的绝对路径，统一使用正斜杠，去掉./前缀
            normalized_path = image_path.replace('\\', '/')
            # 去掉开头的./或.\
            if normalized_path.startswith('./'):
                normalized_path = normalized_path[2:]
            elif normalized_path.startswith('.\\'):
                normalized_path = normalized_path[3:]
            return f'<img src="{normalized_path}" alt="image" />'
        except Exception as e:
            print(f"保存图片失败: {e}")
            return match.group(0)  # 保持原内容不变
    
    # 6. 替换所有内联图片
    updated_content = re.sub(pattern, replace_image, content)
    
    return updated_content


def convert_file_inline_images(file_path, output_dir=None, image_prefix="img", save_updated_file=True):
    """
    将文件中的内联图片转换为本地图片，并可选择保存更新后的文件。

    :param file_path: 输入文件路径
    :param output_dir: 图片输出目录（可选）
    :param image_prefix: 图片文件名前缀（可选）
    :param save_updated_file: 是否保存更新后的文件（可选），默认为True
    :return: 更新后的文件内容字符串，如果save_updated_file为True则同时保存到文件
    """
    # 转换内联图片
    updated_content = convert_inline_images_to_local(file_path, output_dir, image_prefix)
    
    # 如果需要保存更新后的文件
    if save_updated_file:
        # 生成备份文件名
        backup_path = f"{file_path}.backup"
        
        # 备份原文件
        try:
            import shutil
            shutil.copy2(file_path, backup_path)
            print(f"原文件已备份为: {backup_path}")
        except Exception as e:
            print(f"备份原文件失败: {e}")
        
        # 保存更新后的文件
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            print(f"文件已更新: {file_path}")
        except Exception as e:
            raise ValueError(f"保存更新后的文件失败: {e}") from e
    
    return updated_content

