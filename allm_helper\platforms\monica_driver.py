import logging
import re
import time
from playwright.sync_api import Page

from allm_helper.platforms.base_platform import BasePlatformDriver
from autils.pw_clipboard_helper import UniversalClipboardInterceptor

logger = logging.getLogger(__name__)
# console.log([...document.querySelectorAll('span[class*="bot-name--"] > span')].map(el => el.textContent.trim()));
MODELS = ['Monica',
          'Claude 4 Sonnet',
          'Claude 4 Opus',
          'DeepSeek V3',
          'DeepSeek R1',
          'Grok 3',
          'GPT-4o',
          'GPT-4.1',
          'Claude 4 Sonnet Thinking',
          'Claude 4 Opus Thinking',
          'Claude 3.7 Sonnet',
          'Claude 3.7 Sonnet Thinking',
          'Claude 3.5 Sonnet V2',
          'o3',
          'o4-mini',
          'Perplexity-reasoning',
          'Perplexity',
          'Claude 3.5 Haiku',
          'GPT-4.1 mini',
          'GPT-4.1 nano',
          'GPT-4o mini',
          'Gemini 2.5 Flash',
          'Gemini 2.5 Pro',
          'Qwen3 235B',
          'Llama 3.1 405B',
          'GPT-4',
          'DALL·E 3',
          'GPT-4.5'
          ]


class MonicaDriver(BasePlatformDriver):
    """
    Monica平台的具体驱动实现 (基于用户提供的 monica_helper.py)。
    """
    # --- 配置区域: 从 monica_helper.py 迁移的URL和选择器 ---
    CHAT_URL = "https://monica.im/home"  # 聊天交互页面

    def __init__(self, page: Page):
        """
        初始化 Monica 驱动，只接受 page 参数。
        模型配置将在 new_conversation 时传入。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)
        # 等待页面加载完成，确保输入框可见
        self.page.wait_for_selector("textarea[data-input_node=monica-chat-input]:visible")
        # 存储当前模型配置，用于后续会话
        self.current_model_config = {}

    def _select_llm(self, model_name: str):
        self.page.locator("[class*='bot-switcher--'] > span[class*='bot-info--']:visible").click()
        self.page.locator("[id*=monica-bots-views-item-]").get_by_text(model_name, exact=True).click()

    def _use_temp_chat(self):
        self.page.locator("div[class*=chat-toolbar-right--] span[class*=chat-toolbar-item--]:visible").first.click()
        self.page.locator("div").filter(has_text=re.compile(r"^临时聊天$")).locator("div").nth(1).click()

    def new_conversation(self, model_config: dict = None):
        """
        创建新会话，并应用模型配置。

        Args:
            model_config: 模型配置字典，包含：
                - model_name: 模型名称
                - temp_chat: 是否使用临时聊天
                - 其他 Monica 特定参数
        """
        # Monica UI中，开始新会话最可靠的方式是快捷键
        self.page.goto("https://monica.im/home/<USER>")
        self.page.locator("textarea[data-input_node=monica-chat-input]:visible").click()
        self.page.keyboard.press("Control+K")
        self.page.get_by_text("新会话", exact=True).click()
        logger.info("已开始新会话。")

        # 更新当前模型配置
        if model_config:
            self.current_model_config.update(model_config)

        # 应用模型配置
        if self.current_model_config.get('model_name'):
            logger.info(f"正在选择模型: {self.current_model_config['model_name']}")
            self._select_llm(self.current_model_config['model_name'])
        if self.current_model_config.get('sub_model_name'):
            logger.info(f"正在选择子模型: {self.current_model_config['sub_model_name']}")
            self.select_sub_model(self.current_model_config['sub_model_name'])
        if self.current_model_config.get('temp_chat'):
            logger.info("正在切换到临时聊天。")
            self._use_temp_chat()
        if model_config.get('system_prompt'):
            self.chat(model_config['system_prompt'])
        return self

    def select_sub_model(self, model_name: str):
        self.page.locator("[class^='label-wrapper--']").first.click()
        time.sleep(1)
        target = self.page.get_by_text(model_name, exact=True).last
        target.scroll_into_view_if_needed()
        target.click()
        self.page.locator("textarea[data-input_node=monica-chat-input]:visible").click()  # 空白区要点一下
        assert self.page.locator(f'span[class*="model-name--"]').last.inner_text() in model_name
        # self.page.locator(f'span[class*="model-name--"]:has-text("{model_name}")').wait_for(state="visible", timeout=3000)

    def use_existing_conversation(self, conversation_title: str):
        logger.info(f"正在查找并切换到会话: '{conversation_title}'...")
        self.page.locator("div[class*=chat-toolbar-right--] span[class*=chat-toolbar-item--]:visible").nth(1).click()
        self.page.get_by_role("textbox", name="搜索", exact=True).fill(conversation_title)
        time.sleep(1)  # 等待搜索结果响应
        self.page.locator("div[class*=history-chats] div[class*=conv-item-wrapper--]").first.click()
        logger.info(f"已切换到会话: '{conversation_title}'")

    def chat(self, prompt: str, attachments: list[str] = None, response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。

        :param prompt: 用户输入的提示。
        :param attachments: 要上传的附件的本地文件路径列表。
        :param response_format: 响应格式，可选 "text" 或 "markdown"或code。
        :return: AI的回复文本。
        """
        # 首先切换到当前驱动的页面
        self.switch_to_page()

        if not self.is_ready():
            raise Exception("平台正忙，无法发送新消息。")

        # 1. 上传附件 (如果需要)
        if attachments:
            with self.page.expect_file_chooser() as fc_info:
                self.page.locator("span[class*=chat-toolbar-item]:visible").first.click()
            file_chooser = fc_info.value
            file_chooser.set_files(attachments)
            logger.info(f"已上传 {len(attachments)} 个文件。")

        # 2. 输入提示并发送
        self.page.locator("textarea[data-input_node=monica-chat-input]:visible").fill(prompt)
        self.page.locator("div[class*=input-msg-btn--]:visible").click()

        # 3. 等待回复完成
        # 等待最后一个回复块出现
        self.page.locator(f"div[class*=chat-items--] > div[class*=chat-reply--]:visible").last.wait_for(timeout=600000)
        # 等待最后一个回复块中的分享按钮出现，这是一个明确的结束标志
        self.page.locator("div[class*=left] > div[class*=share-button--]:visible").last.wait_for(timeout=600000)

        if "code" == response_format:
            return self.page.locator("div[class*=code-enhance-content]").last.inner_text()
        else:

            '''
                copyToClipboard(e) {
                    let t = document.createElement("textarea");
                    t.id = "copy-to-clipboard";
                    let a = document.body;
                    a.appendChild(t),
                    t.oncopy = e => {
                        e.stopPropagation()
                    }
                    ,
                    t.value = e,
                    t.select(),
                    document.execCommand("copy"),
                    a.removeChild(t)
                }
            '''

            def get_return_text():
                # 使用全新的、100%无残留的轮询拦截器
                with UniversalClipboardInterceptor(self.page) as interceptor:
                    self.page.locator("div[class*=left] div[class*=copy-group--]:visible").last.hover()
                    if "markdown" == response_format:
                        target = self.page.get_by_text("复制为 Markdown").last
                        target.scroll_into_view_if_needed()
                        target.click()
                    elif "text" == response_format:
                        target = self.page.get_by_text("复制为纯文本").last
                        target.scroll_into_view_if_needed()
                        target.click()
                    else:
                        assert False, f"不支持的格式{response_format}"
                    interceptor.wait_for_capture()

                return interceptor.text

            # 在你的主逻辑中调用
            return get_return_text()

    def is_ready(self) -> bool:
        # 检查发送按钮是否可见且可用
        return self.page.locator("div[class*=input-msg-btn--]:visible").is_enabled()

    # --- 从 monica_helper.py 迁移的额外方法 ---
    def save_chat(self, chat_name: str):
        """保存当前会话并命名。"""
        self.page.locator("div[class*=chat-toolbar-right--] span[class*=chat-toolbar-item--]:visible").first.click()
        self.page.get_by_role("tooltip").get_by_role("img").first.click()
        self.page.get_by_role("textbox").last.fill(chat_name)
        self.page.get_by_text("好的", exact=True).click()
        self.page.get_by_text("标题已更新", exact=True).wait_for(timeout=30000)
        logger.info(f"会话已保存为: '{chat_name}'")

    def del_chat(self, chat_name: str):
        """根据名称搜索并删除会话。"""
        self.page.locator("div[class*=chat-toolbar-right--] span[class*=chat-toolbar-item--]:visible").nth(1).click()
        self.page.get_by_role("textbox", name="搜索", exact=True).fill(chat_name)
        time.sleep(1)  # 等待搜索结果
        first_item = self.page.locator("div[class*=history-chats] div[class*=conv-item-wrapper--]").first
        first_item.hover()
        first_item.locator("span[class*=operation-ac]").nth(2).click()
        self.page.get_by_text("删除", exact=True).click()
        logger.info(f"会话 '{chat_name}' 已删除。")
