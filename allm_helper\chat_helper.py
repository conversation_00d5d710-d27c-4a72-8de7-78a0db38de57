import logging
import textwrap
from allm_helper.platforms.base_platform import BasePlatformDriver

# 配置日志记录器
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ChatHelper:
    """
    一个高层级的帮助类，用于封装平台驱动，并提供额外的功能，如日志记录。
    """
    def __init__(self, driver: BasePlatformDriver, log_prefix=""):
        """
        使用一个具体的平台驱动来初始化帮助类。

        :param driver: 一个 BasePlatformDriver 的实例 (例如 MonicaDriver, AistudioDriver)。
        """
        self.log_prefix = log_prefix
        self._driver = driver
        self._driver.switch_to_page()

    def chat(self, prompt: str, attachments: list[str] = None, **kwargs) -> str:
        """
        在当前激活的会话中发送消息，并等待、返回AI的完整回复。
        此方法会自动记录输入和输出。

        :param prompt: 您要发送给AI的文本提示。
        :param attachments: (可选) 要附加到消息的文件路径列表。
        :param kwargs: 其他传递给驱动 chat 方法的参数 (例如 response_format)。
        :return: AI回复的字符串。
        """
        # 使用 textwrap.dedent 来处理多行字符串的缩进，使日志更美观
        prompt = textwrap.dedent(prompt).strip()
        
        # 为了日志清晰，使用 repr() 显示换行符等特殊字符
        logger.info(f"{self.log_prefix} chat 输入: \nprompt={prompt}, attachments={attachments}, kwargs={kwargs}\n\n")
        
        # 调用底层驱动的 chat 方法
        result = self._driver.chat(prompt, attachments, **kwargs)
        
        # 为了防止日志过长，只记录部分返回结果
        logger.info(f"{self.log_prefix} chat 输出: \n{result}\n\n")
        
        return result

    # 你也可以将驱动的其他方法在这里进行包装
    def new_conversation(self, model_config: dict = None):
        logger.info(f"{self.log_prefix} 创建新会话: model_config={model_config}")
        self._driver.new_conversation(model_config)

    def use_existing_conversation(self, conversation_title: str):
        logger.info(f"{self.log_prefix} 使用已有会话: {conversation_title}")
        self._driver.use_existing_conversation(conversation_title)

    def is_ready(self) -> bool:
        return self._driver.is_ready()

    def save_chat(self, chat_name: str):
        logger.info(f"{self.log_prefix} 保存会话: {chat_name}")
        self._driver.save_chat(chat_name)

    def del_chat(self, chat_name: str):
        logger.info(f"{self.log_prefix} 删除会话: {chat_name}")
        self._driver.del_chat(chat_name)

    @property
    def driver(self):
        return self._driver

    # 腾讯元宝特有功能的包装方法
    def select_model(self, model_name: str):
        """选择模型（腾讯元宝特有）"""
        if hasattr(self._driver, 'select_model'):
            logger.info(f"{self.log_prefix} 选择模型: {model_name}")
            return self._driver.select_model(model_name)
        else:
            logger.warning(f"{self.log_prefix} 当前驱动不支持模型选择功能")
            return False

    def enable_deep_thinking(self, enabled: bool = True):
        """启用/禁用深度思考模式（腾讯元宝特有）"""
        if hasattr(self._driver, 'enable_deep_thinking'):
            logger.info(f"{self.log_prefix} {'启用' if enabled else '禁用'}深度思考模式")
            return self._driver.enable_deep_thinking(enabled)
        else:
            logger.warning(f"{self.log_prefix} 当前驱动不支持深度思考功能")
            return False

    def enable_web_search(self, enabled: bool = True):
        """启用/禁用联网搜索功能（腾讯元宝特有）"""
        if hasattr(self._driver, 'enable_web_search'):
            logger.info(f"{self.log_prefix} {'启用' if enabled else '禁用'}联网搜索功能")
            return self._driver.enable_web_search(enabled)
        else:
            logger.warning(f"{self.log_prefix} 当前驱动不支持联网搜索功能")
            return False

    def enable_auto_search(self, enabled: bool = True):
        """启用/禁用自动搜索功能（腾讯元宝特有）"""
        if hasattr(self._driver, 'enable_auto_search'):
            logger.info(f"{self.log_prefix} {'启用' if enabled else '禁用'}自动搜索功能")
            return self._driver.enable_auto_search(enabled)
        else:
            logger.warning(f"{self.log_prefix} 当前驱动不支持自动搜索功能")
            return False

    def start_temporary_chat(self):
        """开始临时对话（腾讯元宝特有）"""
        if hasattr(self._driver, 'start_temporary_chat'):
            logger.info(f"{self.log_prefix} 启动临时对话")
            return self._driver.start_temporary_chat()
        else:
            logger.warning(f"{self.log_prefix} 当前驱动不支持临时对话功能")
            return False
