import logging
import textwrap
import time
from playwright.sync_api import Page

from allm_helper.platforms.base_platform import BasePlatformDriver
from autils.pw_clipboard_helper import UniversalClipboardInterceptor

logger = logging.getLogger(__name__)


class AistudioDriver(BasePlatformDriver):
    """
    Google AI Studio 平台的具体驱动实现 (基于 aistudio_helper.py)。
    """
    CHAT_URL = "https://aistudio.google.com/app/prompts/new_chat"

    def __init__(self, page: Page):
        """
        初始化 AI Studio 驱动，只接受 page 参数。
        模型配置将在 new_conversation 时传入。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)

        # 默认配置值
        self.current_model_config = {
            'system_prompt': '',
            'temperature': 1.0,
            'top_p': 0.95,
            'model_type': "GEMINI 2.5",
            'model_name': "Gemini 2.5 Flash"
        }

        # 状态追踪
        self.first_upload = True
        self.support_system_prompt = False
        self.initial_prompt_prefix = ""

        # 超时设置
        self.upload_files_timeout = 5000
        self.wait_reply_timeout = 5000

        # 响应格式
        self.context_format = "markdown"

    def _apply_conversation_settings(self):
        """应用会话设置，如模型、温度、系统提示等"""
        self._select_llm(self.current_model_config['model_name'])
        self._update_settings(self.current_model_config['temperature'], self.current_model_config['top_p'])
        system_prompt = textwrap.dedent(self.current_model_config.get('system_prompt', ''))
        if system_prompt:
            if self.support_system_prompt:
                self._update_system_prompt(system_prompt)
            else:
                self.initial_prompt_prefix = f"{system_prompt}\n\n问题：\n"

    def _select_llm(self, model_name: str):
        """选择LLM模型"""
        self.page.get_by_role("combobox").first.locator("svg").click()
        self.page.get_by_text(self.current_model_config['model_type'], exact=True).click()
        self.page.locator("div.model-options-panel-content").get_by_text(model_name, exact=True).click()
        self.support_system_prompt = self._is_support_system_prompt()

    def _is_support_system_prompt(self):
        """检查是否支持系统提示词"""
        button = self.page.locator("button[data-test-si]")
        return not button.get_attribute('aria-disabled') == "true"  # 可点击，系统提示词功能可用

    def _update_system_prompt(self, system_prompt):
        """更新系统提示词"""
        button = self.page.locator("button[data-test-si]")
        button.click()
        self.page.get_by_role("textbox", name="System instructions").fill(system_prompt)
        self.page.get_by_role("button", name="System instructions", exact=True).click()

    def _update_settings(self, temperature, top_p):
        """更新模型参数设置"""
        self.page.get_by_role("spinbutton").last.fill(str(temperature))
        self.page.get_by_role("button", name="Expand or collapse advanced").click()
        self.page.get_by_title("Top P set of tokens to").get_by_role("spinbutton").fill(str(top_p))
        self.page.get_by_role("button", name="Expand or collapse advanced").click()

    def _scroll_to_bottom(self, scroll_container):
        """滚动到容器底部"""
        if scroll_container.is_visible():
            scroll_container.evaluate('''(element) => {
                element.scrollTop = element.scrollHeight;
                const event = new Event('scroll');
                element.dispatchEvent(event);
            }''')

    def new_conversation(self, model_config: dict = None):
        """
        创建新会话，并应用模型配置。

        Args:
            model_config: 模型配置字典，包含：
                - model_name: 模型名称
                - model_type: 模型类型
                - temperature: 温度参数
                - top_p: Top-P 参数
                - system_prompt: 系统提示词
                - 其他 AI Studio 特定参数
        """
        self.page.get_by_role("link", name="Chat", exact=True).click()
        self.initial_prompt_prefix = ""

        # 更新当前模型配置
        if model_config:
            self.current_model_config.update(model_config)

        self._apply_conversation_settings()
        return self

    def use_existing_conversation(self, conversation_title: str):
        """使用已有会话"""
        logger.info(f"正在查找并切换到会话: '{conversation_title}'...")
        self.page.goto("https://aistudio.google.com/app/library")
        # self.page.get_by_role("link", name="History").click()

        self.page.get_by_role("cell", name="chat_bubble").last.wait_for(state="visible")

        self.page.get_by_role("textbox", name="Search field for prompts").fill(conversation_title)
        self.page.get_by_role("textbox", name="Search field for prompts").press("Enter")

        # 点击后等待对话页面加载完成
        self.page.get_by_role("cell", name="chat_bubble").first.click()
        self.page.locator("div[class*='prompt-input-wrapper-container']").last.wait_for(state="visible")
        # self.page.get_by_role("textbox").last.wait_for(state="visible")

        self.initial_prompt_prefix = ""
        logger.info(f"已切换到会话: '{conversation_title}'")

    def _upload_files(self, files):
        """上传文件"""
        self.page.get_by_role("button", name="Insert assets such as images").click()
        with self.page.expect_file_chooser() as fc_info:
            self.page.get_by_role("menuitem", name="Upload File").click()
        file_chooser = fc_info.value
        file_chooser.set_files(files)

        # 第一次上传需要同意版权声明
        if self.first_upload:
            try:
                self.page.get_by_role("button", name="Agree to the copyright").click(timeout=5000)
                self.first_upload = False
            except:
                pass

        # 处理上传错误
        try:
            self.page.locator("div[class*='toast'] span[class='mat-focus-indicator']").click(
                timeout=self.upload_files_timeout)
            self.page.locator("div.model-error").wait_for(timeout=self.upload_files_timeout)
            return False  # 上传失败
        except:
            return True  # 上传成功

    def _wait_for_response_text_stable(self):
        """等待响应文本稳定下来，防止复制不完整内容。"""
        self._scroll_to_bottom(self.page.locator("ms-autoscroll-container"))

        previous_text = None
        # 使用30秒的超时来避免无限循环
        start_time = time.time()
        timeout = 30

        while time.time() - start_time < timeout:
            try:
                current_text = self.page.locator("div.model-prompt-container").last.inner_text()
                if previous_text is not None and previous_text == current_text and len(current_text) > 0:
                    logger.debug("响应文本已稳定。")
                    return
                previous_text = current_text
                time.sleep(1)
            except Exception:
                # 元素可能在轮询期间暂时消失，稍后重试
                time.sleep(1)

        logger.warning("等待响应文本稳定超时，将使用当前获取到的内容。")

    def _wait_chat_finished(self):
        """等待聊天响应完成，处理可能的错误并重试。"""
        max_retries = 5
        for i in range(max_retries):
            try:
                model_error = self.page.locator("div.model-error").last
                model_error.hover(timeout=1000)  # 确认错误可见
                self.page.get_by_role("button", name="Dismiss").last.click(timeout=1000)
                self.page.evaluate("""() => {
                    var maxIntervalId = setInterval(function(){}, 10000);
                    for (var i = 1; i <= maxIntervalId; i++) {
                        clearInterval(i);
                    }
                    console.log('All setInterval timers cleared up to ID ' + maxIntervalId);
                }""")
                logger.error("检测到模型错误，尝试重试...")
                self.page.locator("ms-chat-turn").get_by_label("Rerun this turn").last.click(force=True, timeout=1000)
                time.sleep(5)  # 等待重试启动
                continue  # 继续下一次循环等待
            except Exception:
                pass  # 没有模型错误，继续检查其他情况

            # 检查并处理toast通知
            try:
                self.page.get_by_text("error An internal error has").wait_for(timeout=2000)
                # self.page.locator("div[class='turn-content'] span[class='mat-focus-indicator']").click(timeout=1000)
                self.page.locator("ms-chat-turn").get_by_label("Rerun this turn").last.click(force=True, timeout=1000)
                logger.warning("关闭了一个toast通知，将继续等待响应。")
                continue
            except Exception:
                pass  # 没有toast

            # 主要的完成标志是 "Good response" 按钮
            try:
                self.page.get_by_role("button", name="Good response").wait_for(timeout=60000)
            except:
                print("_wait_chat_finished，重试")
                continue
            self._wait_for_response_text_stable()
            return  # 成功完成，退出函数

    def _get_reply(self):
        self.page.locator("div.model-prompt-container").last.hover()
        self.page.locator("ms-chat-turn").last.get_by_label("Open options").last.click()

        def get_return_text():
            # 使用全新的、100%无残留的轮询拦截器
            with UniversalClipboardInterceptor(self.page) as interceptor:
                if self.context_format == "text":
                    self.page.get_by_role("menuitem", name="Copy text").last.click()
                else:
                    self.page.get_by_role("menuitem", name="Copy markdown").last.click()
                # self.page.get_by_role("button", name="Close settings").last.click()
                interceptor.wait_for_capture()

            return interceptor.text

        return get_return_text()

    def chat(self, prompt: str, attachments: list[str] = None, response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。

        :param prompt: 用户输入的提示。
        :param attachments: 要上传的附件的本地文件路径列表。
        :param response_format: 响应格式，可选 "text"。
        :return: AI的回复文本。
        """
        # 首先切换到当前驱动的页面
        self.switch_to_page()

        if not self.is_ready():
            raise Exception("平台正忙，无法发送新消息。")
        self.page.get_by_role("textbox").last.wait_for(state="visible", timeout=10000)
        # 如果模型不支持，则将系统提示词拼接到首次prompt
        if self.initial_prompt_prefix:
            full_prompt = self.initial_prompt_prefix + prompt
            self.initial_prompt_prefix = ""  # 仅使用一次
        else:
            full_prompt = prompt

        # 1. 上传附件 (如果需要)
        if attachments:
            upload_success = self._upload_files(attachments)
            if not upload_success:
                raise Exception("文件上传失败")

        # 2. 输入提示
        self.page.get_by_role("textbox").last.fill(textwrap.dedent(full_prompt))

        # 3. 发送消息
        start = time.time()
        self.page.get_by_role("button", name="Run", exact=True).last.click()
        logger.info(f"aistudio run wait: {time.time() - start}")

        # 4. 等待回复完成
        self._wait_chat_finished()

        # 5. 获取回复
        return self._get_reply()

    def is_ready(self) -> bool:
        """检查平台是否准备好接收新消息"""
        # 检查输入框和发送按钮是否可用
        return True
        # textbox = self.page.get_by_role("textbox").last
        # return textbox.is_visible()

    def save_chat(self, chat_name: str):
        """保存当前会话并命名"""
        # self.page.get_by_role("button", name="Save prompt").click()
        self.page.get_by_role("button", name="Edit prompt title and").click()
        self.page.get_by_role("textbox", name="Prompt name text field").fill(chat_name)
        self.page.get_by_role("button", name="Save title and description").click()
        # 等待保存对话框消失，而不是固定延时
        self.page.get_by_role("button", name="Save title and description").wait_for(state="hidden", timeout=10000)
        logger.info(f"会话已保存为: '{chat_name}'")

    def del_chat(self, chat_name: str):
        """删除会话 (AI Studio暂不支持)"""
        logger.warning(f"Google AI Studio 暂不支持删除会话功能")

    def autosave(self, enable=True):
        logger.info(f"autosave: '{enable}'")
        self.page.locator("[data-test-id=\"settings-menu\"]").click()
        is_enabled = self.page.get_by_role("switch").last.get_attribute('aria-checked') == "true"
        if enable != is_enabled:
            self.page.get_by_role("switch").last.click()
        self.page.locator(".cdk-overlay-backdrop").last.click()
        # self.page.locator("[data-test-id=\"settings-menu\"]").click() # 点不到

    def thinking_mode(self, enable=True):
        logger.info(f"thinking_mode: '{enable}'")
        button = self.page.get_by_role("switch", name="Toggle thinking mode").last
        is_enabled = button.last.get_attribute('aria-checked') == "true"
        if enable != is_enabled:
            button.click()

    def url_context(self, enable=True):
        logger.info(f"thinking_mode: '{enable}'")
        button = self.page.get_by_role("switch", name="Browse the url context").last
        is_enabled = button.last.get_attribute('aria-checked') == "true"
        if enable != is_enabled:
            button.click()

    def structured_output(self, enable=True):
        logger.info(f"Structured output: '{enable}'")
        button = self.page.get_by_role("switch", name="Structured output").last
        is_enabled = button.last.get_attribute('aria-checked') == "true"
        if enable != is_enabled:
            button.click()
