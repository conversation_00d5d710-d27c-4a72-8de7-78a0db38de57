import re

from autils.random_helper import my_nanoid_id


def convert_fill_blanks(text):
    """
    将填空题格式从 ____答案____ 转换为 <span class="fill_blank" contenteditable="false" data-blank-id="随机ID">答案</span>

    Args:
        text: 包含填空题格式的文本

    Returns:
        转换后的文本
    """
    if not text:
        return text

    # 匹配 ____答案____ 格式
    pattern = r'____([^_]+)____'

    def replace_blank(match):
        answer = match.group(1)
        blank_id = my_nanoid_id()
        return f'<span class="fill_blank" contenteditable="false" data-blank-id="{blank_id}">{answer}</span>'

    return re.sub(pattern, replace_blank, text)
