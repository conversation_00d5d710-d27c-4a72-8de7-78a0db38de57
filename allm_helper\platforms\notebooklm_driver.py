import logging
from typing import Optional, List

from playwright.sync_api import Page, expect

from allm_helper.platforms.base_platform import BasePlatformDriver

logger = logging.getLogger(__name__)


class NotebookLMDriver(BasePlatformDriver):
    """
    Google NotebookLM 平台的具体驱动实现。
    """
    CHAT_URL = "https://notebooklm.google.com/notebook"

    def __init__(self, page: Page):
        """
        初始化 NotebookLM 驱动。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)


    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建一个新的 Notebook 作为新会话。
        """
        logger.info("正在 NotebookLM 中创建新笔记本...")
        try:
            self.page.locator('button[aria-label="新建笔记本"]').click()
            # 等待新笔记本的查询框出现
            expect(self.page.get_by_role("textbox", name="查询框")).to_be_visible(timeout=15000)
            logger.info("已成功创建新笔记本。")
        except Exception as e:
            logger.error(f"创建新笔记本失败: {e}")
            raise
        return self

    def use_existing_conversation(self, conversation_title: str):
        """
        切换到指定的 Notebook。
        """
        logger.info(f"正在切换到笔记本: {conversation_title}")
        try:
            # 返回到笔记本列表页面
            self.page.goto(self.CHAT_URL)
            self.page.wait_for_selector('button:has-text("New notebook")', timeout=20000)
            # 点击指定标题的笔记本
            self.page.get_by_role("link", name=conversation_title).click()
            # 等待查询框出现
            expect(self.page.get_by_role("textbox", name="查询框")).to_be_visible(timeout=15000)
            logger.info(f"已成功切换到笔记本: {conversation_title}")
        except Exception as e:
            logger.error(f"切换到笔记本 '{conversation_title}' 失败: {e}")
            raise

    def chat(self, prompt: str, attachments: Optional[List[str]] = None,
             response_format: str = "text", **kwargs) -> str:
        """
        在当前 Notebook 中发送消息并获取回复。
        """
        self.switch_to_page()

        if not self.is_ready():
            raise Exception("平台正忙，无法发送新消息。")
            
        if attachments:
            logger.warning("NotebookLM 驱动暂不支持在聊天中上传附件。请先将文件作为来源添加到 Notebook。")

        logger.info("正在发送提示...")
        try:
            textbox = self.page.get_by_role("textbox", name="查询框").last
            textbox.fill(prompt)
            # 点击提交按钮
            self.page.get_by_role("button", name="提交").last.click()
            logger.info("提示已发送。")
        except Exception as e:
            logger.error(f"发送提示失败: {e}")
            raise

        logger.info("正在等待 NotebookLM 回复...")
        try:
            # 等待加载指示器消失
            loading_indicator = self.page.locator("loading-component div").last
            loading_indicator.wait_for(state="hidden", timeout=180000)
            # 额外的等待确保内容完全渲染
            self.page.wait_for_timeout(1000)
            logger.info("NotebookLM 回复已接收。")
        except Exception as e:
            logger.error(f"等待回复超时或失败: {e}")
            raise

        logger.info("正在提取回复内容...")
        # 内容在 mat-card-content 元素中
        response_content = self.page.locator("mat-card-content").last
        return response_content.inner_text()

    def is_ready(self) -> bool:
        """
        检查平台是否准备好接收新消息。
        """
        try:
            submit_button = self.page.get_by_role("button", name="提交").last
            return submit_button.is_enabled()
        except Exception:
            return False

    def save_chat(self, chat_name: str):
        """
        NotebookLM 通过笔记本的标题来命名，此功能暂不单独实现。
        请在创建或切换笔记本时使用正确的标题。
        """
        logger.warning("请通过 use_existing_conversation(notebook_title) 或 new_conversation() 来管理笔记本。")
        pass

    def del_chat(self, chat_name: str):
        """
        删除指定的 Notebook。
        """
        logger.info(f"正在删除笔记本: {chat_name}")
        try:
            # 返回到笔记本列表页面
            self.page.goto(self.CHAT_URL)
            self.page.wait_for_selector('button:has-text("New notebook")', timeout=20000)
            
            # 找到对应的笔记本并删除
            notebook_entry = self.page.locator("a.notebook-link", has_text=chat_name).first
            notebook_entry.hover()
            # 点击更多选项按钮
            notebook_entry.locator('button[aria-label="More options"]').click()
            # 点击删除按钮
            self.page.get_by_role("menuitem", name="Delete").click()
            # 确认删除
            self.page.get_by_role("button", name="Delete").click()
            
            # 等待删除成功的提示或列表刷新
            expect(self.page.get_by_text(f'"{chat_name}" moved to trash')).to_be_visible(timeout=10000)
            logger.info(f"笔记本 '{chat_name}' 已被删除。")
        except Exception as e:
            logger.error(f"删除笔记本 '{chat_name}' 失败: {e}")
            raise 