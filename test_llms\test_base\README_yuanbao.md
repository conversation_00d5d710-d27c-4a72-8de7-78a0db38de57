# 腾讯元宝平台驱动说明

本文档说明了腾讯元宝平台驱动的实现和使用方法。

## 驱动文件位置

- 驱动实现：`allm_helper/platforms/yuanbao_driver.py`
- 测试文件：`test_llms/test_base/test_yuanbao.py`

## 功能特性

### 已实现的功能

1. **基础聊天功能**
   - 支持文本对话
   - 支持中文和英文交互
   - 支持多轮对话

2. **配置支持**
   - 模型名称配置（默认：混元大模型）
   - 系统提示词支持（通过前缀方式）
   - 温度和top_p参数配置

3. **平台检测**
   - 检查平台是否就绪
   - 输入框可用性检测

4. **错误处理**
   - 网络错误处理
   - 输入验证
   - 超时处理

### 待完善的功能

1. **文件上传**
   - 框架已实现，需要根据实际界面调整选择器
   - 支持多种文件类型

2. **会话管理**
   - 保存会话功能
   - 历史会话切换
   - 会话删除功能

3. **高级功能**
   - 系统提示词直接设置（如果平台支持）
   - 模型参数实时调整
   - 响应格式选择

## 使用方法

### 基础使用

```python
from allm_helper.llm_begin_helper import chat_helper_factory

# 创建腾讯元宝客户端
yuanbao_agent = chat_helper_factory(
    platform_name="yuanbao",
    user_email="not_login",  # 腾讯元宝可能支持免登录
    conversation_params={
        "model_name": "混元大模型"
    }
)

# 发送消息
response = yuanbao_agent.chat("你好，请介绍一下你自己")
print(response)
```

### 带系统提示词的使用

```python
yuanbao_agent = chat_helper_factory(
    platform_name="yuanbao",
    user_email="not_login",
    conversation_params={
        "system_prompt": "你是一个专业的AI助手，请简洁地回答问题。",
        "model_name": "混元大模型",
        "temperature": 0.7,
        "top_p": 0.9
    }
)

response = yuanbao_agent.chat("什么是人工智能？")
```

## 运行测试

```bash
# 运行所有腾讯元宝测试
G:/yjxt2025/bpllm/venv/Scripts/python.exe -m pytest test_llms/test_base/test_yuanbao.py -v

# 运行特定测试
G:/yjxt2025/bpllm/venv/Scripts/python.exe -m pytest test_llms/test_base/test_yuanbao.py::test_basic_chat -v

# 运行测试并显示详细输出
G:/yjxt2025/bpllm/venv/Scripts/python.exe -m pytest test_llms/test_base/test_yuanbao.py -v -s
```

## 技术实现细节

### 选择器策略

驱动使用多重选择器策略来适应不同的界面变化：

```python
# 输入框选择器
input_selectors = [
    'textarea[placeholder*="输入"]',
    'textarea[placeholder*="问题"]',
    '.chat-input textarea',
    'input[type="text"]',
    '[contenteditable="true"]'
]

# 发送按钮选择器
send_selectors = [
    'button[aria-label="发送"]',
    'button:has-text("发送")',
    '.send-btn',
    '[data-testid="send"]'
]
```

### 错误处理机制

1. **超时处理**：设置合理的超时时间
2. **重试机制**：对于网络错误进行重试
3. **降级策略**：当某些功能不可用时提供替代方案

### 响应获取策略

1. **直接文本获取**：通过DOM元素获取响应文本
2. **复制功能**：使用剪贴板拦截器获取内容
3. **多重选择器**：适应不同的界面结构

## 调试和完善

### 需要调整的地方

1. **界面选择器**：根据腾讯元宝的实际界面调整CSS选择器
2. **登录检测**：完善登录状态检测逻辑
3. **响应完成检测**：优化响应完成的判断机制

### 调试方法

1. 使用浏览器开发者工具检查实际的DOM结构
2. 通过日志输出调试选择器匹配情况
3. 使用Playwright的调试模式观察页面交互

### 常见问题

1. **选择器不匹配**：检查页面DOM结构，更新选择器
2. **登录问题**：确认登录状态检测逻辑
3. **响应获取失败**：检查响应内容的DOM结构

## 后续开发计划

1. **完善文件上传功能**
2. **实现会话管理功能**
3. **优化错误处理机制**
4. **添加更多配置选项**
5. **提升稳定性和性能**
