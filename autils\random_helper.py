import random
import time

import nanoid


def my_nanoid_id() -> str:
    """
    为题库生成一个唯一、安全且美观的ID。
    ID由纯字母和数字组成，长度为22位，以确保其
    唯一性强度不低于NanoID默认配置。
    """
    ALPHABET = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
    return nanoid.generate(alphabet=ALPHABET, size=12)


def generate_mysql_int_unique_id():
    """
    生成一个在MySQL INT字段（正数范围）内的伪全局唯一ID，同时避免直接暴露出生成时间。
    该方法通过将当前时间戳（毫秒）与一个大范围随机数进行异或操作，并确保结果在INT的正数范围内。
    异或操作使得从ID逆推原始时间戳变得困难。此方法无法保证在极端高并发或长时间运行下的绝对全局唯一性。
    对于真正的全局唯一ID，建议使用BIGINT存储UUID或类似Snowflake算法生成的ID。
    """
    # 获取当前时间戳（精确到毫秒）
    timestamp_ms = int(time.time() * 1000)

    # MySQL INT的正数范围是 1 到 2,147,483,647
    max_int = 2**31 - 1 # 2147483647

    # 生成一个与INT正数范围相当的大随机数
    random_component = random.randint(0, max_int)

    # 将时间戳与随机数进行异或操作以混淆时间信息
    # 然后对 max_int + 1 取模，确保ID在INT正数范围内（避免生成0）
    # 如果结果为0，则重新生成或特殊处理，这里简单地确保不为0
    unique_id = (timestamp_ms ^ random_component) % (max_int + 1)
    if unique_id == 0:
        unique_id = random.randint(1, max_int) # 确保不为0

    return unique_id


def generate_mysql_bigint_unique_id():
    """
    生成一个在MySQL BIGINT字段（正数范围）内的伪全局唯一ID，同时避免直接暴露出生成时间。
    该方法通过将当前时间戳（毫秒）与一个大范围随机数进行异或操作，并确保结果在BIGINT的正数范围内。
    异或操作使得从ID逆推原始时间戳变得困难。此方法为单机生成方案，不包含分布式ID算法中的工作机器ID或序列号。
    对于极端高并发或分布式环境下的绝对全局唯一ID，建议考虑Snowflake等专业ID生成算法。
    """
    # 获取当前时间戳（精确到毫秒）
    timestamp_ms = int(time.time() * 1000)

    # MySQL BIGINT的正数范围是 1 到 9,223,372,036,854,775,807 (2**63 - 1)
    max_bigint = 2**63 - 1

    # 生成一个与BIGINT正数范围相当的大随机数
    # 由于Python的int支持任意精度，random.randint可以处理这么大的范围
    random_component = random.randint(0, max_bigint)

    # 将时间戳与随机数进行异或操作以混淆时间信息
    # 然后对 max_bigint + 1 取模，确保ID在BIGINT正数范围内（避免生成0）
    # 如果结果为0，则重新生成或特殊处理，这里简单地确保不为0
    unique_id = (timestamp_ms ^ random_component) % (max_bigint + 1)
    if unique_id == 0:
        unique_id = random.randint(1, max_bigint) # 确保不为0

    return unique_id
