import logging
from typing import Optional, List

from playwright.sync_api import Page, expect

from allm_helper.platforms.base_platform import BasePlatformDriver

logger = logging.getLogger(__name__)


class CozeCNDriver(BasePlatformDriver):
    """
    Coze 国内版平台的具体驱动实现。
    """
    CHAT_URL = "https://www.coze.cn/space"

    def __init__(self, page: Page):
        """
        初始化 CozeCN 驱动。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL, wait_until="domcontentloaded")

    def switch_to_bot(self, bot_name: str):
        """
        切换到指定的 Bot。
        """
        logger.info(f"正在切换到 Bot: {bot_name}")
        try:
            self.page.get_by_role("link", name=bot_name).first.click()
            # 等待聊天输入框出现
            expect(self.page.locator('textarea[placeholder*="和 Bot 对话"]')).to_be_visible(timeout=15000)
            logger.info(f"已成功切换到 Bot: {bot_name}")
        except Exception as e:
            logger.error(f"切换 Bot '{bot_name}' 失败: {e}")
            raise

    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建新会话。在 Coze 中，这通常意味着选择一个 Bot 并开始对话。
        如果需要切换 Bot，请在 model_config 中提供 'bot_name'。
        """
        bot_name = model_config.get("bot_name") if model_config else None
        if bot_name:
            self.switch_to_bot(bot_name)
        else:
            logger.info("未指定 Bot 名称，将使用当前页面。")

        logger.info("正在创建新会话（清除聊天记录）...")
        try:
            # Coze 通过清除记录来开始新会话
            more_button = self.page.get_by_role("button", name="more")
            if more_button.is_visible():
                more_button.click()
                self.page.get_by_role("button", name="删除对话记录").click()
                self.page.get_by_role("button", name="confirm").click()
                # 等待确认提示消失
                expect(self.page.get_by_text("已删除对话记录")).to_be_visible(timeout=5000)
                logger.info("对话记录已清除，新会话已准备就绪。")
            else:
                logger.info("未找到'更多'按钮，可能已经是新会话。")
        except Exception as e:
            # 如果没有历史记录，按钮可能不存在，这不是一个错误
            logger.warning(f"清除对话记录时出错（可能没有历史记录）: {e}")
        
        return self

    def use_existing_conversation(self, conversation_title: str):
        """
        Coze 不支持按标题切换会话，此功能暂不实现。
        """
        logger.warning(f"CozeCN 驱动暂不支持按标题 '{conversation_title}' 切换会话。")
        pass

    def chat(self, prompt: str, attachments: Optional[List[str]] = None,
             response_format: str = "text", **kwargs) -> str:
        """
        在当前 Bot 对话中发送消息并获取回复。
        """
        self.switch_to_page()

        if not self.is_ready():
            raise Exception("平台正忙，无法发送新消息。")
        
        if attachments:
            logger.warning("CozeCN 驱动暂不支持上传附件。")

        logger.info("正在发送提示...")
        try:
            # 使用 placeholder 定位输入框
            input_textarea = self.page.locator('textarea[placeholder*="和 Bot 对话"]')
            input_textarea.fill(prompt)
            # 点击发送按钮
            send_button = self.page.locator('button[aria-label="send"]')
            send_button.click()
            logger.info("提示已发送。")
        except Exception as e:
            logger.error(f"发送提示失败: {e}")
            raise

        logger.info("正在等待 CozeCN 回复...")
        try:
            # 等待最后一个回复块中的分享按钮出现，这是一个明确的结束标志
            self.page.locator('.chat-message-actions button[aria-label="share"]').last.wait_for(state="visible", timeout=180000)
            logger.info("CozeCN 回复已接收。")
        except Exception as e:
            logger.error(f"等待回复超时或失败: {e}")
            raise

        logger.info("正在提取回复内容...")
        # 回复内容通常在 <pre> 标签或者特定的 div 中
        last_response_group = self.page.locator('.chat-message-content').last
        # 尝试多种可能的内容容器
        code_block = last_response_group.locator('pre').last
        text_block = last_response_group.locator('div[class*="render-lark-card-node"]').last

        if code_block.is_visible():
             return code_block.inner_text()
        elif text_block.is_visible():
            return text_block.inner_text()
        else:
            # 作为备用方案，返回整个回复块的文本
            return last_response_group.inner_text()


    def is_ready(self) -> bool:
        """
        检查平台是否准备好接收新消息。
        """
        try:
            send_button = self.page.locator('button[aria-label="send"]')
            return send_button.is_enabled()
        except Exception:
            return False

    def save_chat(self, chat_name: str):
        """
        Coze 不支持命名会话，此功能暂不实现。
        """
        logger.warning(f"CozeCN 驱动暂不支持将会话保存为 '{chat_name}'。")
        pass

    def del_chat(self, chat_name: str):
        """
        Coze 不支持按名称删除会话，此功能暂不实现。
        """
        logger.warning(f"CozeCN 驱动暂不支持删除名为 '{chat_name}' 的会话。")
        pass 