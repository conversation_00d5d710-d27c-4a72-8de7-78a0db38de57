import time

import threading
from playwright.sync_api import Frame, Page
from typing import Optional
from typing import Union


class UniversalClipboardInterceptor:
    """
    一个通过纯JS轮询实现的、工业级的"全能"同步剪贴板拦截器。
    它不使用 expose_function，能同时拦截 document.execCommand('copy') 和
    navigator.clipboard.writeText，并保证100%的资源清理。
    """

    def __init__(self, context: Union[Page, Frame]):
        self._context = context
        self._namespace = f"__playwright_universal_interceptor_{id(self)}_{threading.get_ident()}"
        self.text: Optional[str] = None

    def __enter__(self):
        js_install_script = f"""
        () => {{
            const ns = '{self._namespace}';
            window[ns] = {{
                text: null,
                copyListener: null,
                originalWriteText: null,
            }};
            const dataStore = window[ns];

            // --- 拦截机制 1: document.execCommand('copy') ---
            const copyListener = (event) => {{
                dataStore.text = window.getSelection().toString();
                event.stopImmediatePropagation();
                event.preventDefault(); // 关键：阻止默认复制行为
            }};
            dataStore.copyListener = copyListener;
            document.addEventListener('copy', copyListener, {{ capture: true }});

            // --- 拦截机制 2: navigator.clipboard.writeText ---
            // 确保 navigator.clipboard 对象存在
            if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {{
                // 保存原始函数，并确保 this 指向正确
                dataStore.originalWriteText = navigator.clipboard.writeText.bind(navigator.clipboard);

                // 重写 writeText 函数
                navigator.clipboard.writeText = async (text) => {{
                    // 将文本存入我们的数据存储区
                    dataStore.text = text;
                    // 返回一个 resolved Promise，让调用方以为操作成功了
                    return Promise.resolve();
                }};
            }}
        }}
        """
        self._context.evaluate(js_install_script)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        js_cleanup_script = f"""
        () => {{
            const ns = '{self._namespace}';
            const dataStore = window[ns];
            if (!dataStore) return;

            // 清理 execCommand 的监听器
            if (dataStore.copyListener) {{
                document.removeEventListener('copy', dataStore.copyListener, {{ capture: true }});
            }}

            // 恢复原始的 writeText 函数
            if (dataStore.originalWriteText) {{
                navigator.clipboard.writeText = dataStore.originalWriteText;
            }}

            // 彻底删除命名空间
            delete window[ns];
        }}
        """
        self._context.evaluate(js_cleanup_script)

    def wait_for_capture(self, timeout: float = 5.0):
        start_time = time.time()
        while time.time() - start_time < timeout:
            js_poll_script = f"() => window['{self._namespace}'] ? window['{self._namespace}'].text : null"
            captured_text = self._context.evaluate(js_poll_script)

            if captured_text is not None and captured_text is not "":
                self.text = captured_text
                return

            time.sleep(0.1)

        raise TimeoutError(f"通过轮询在 {timeout} 秒内未能捕获到任何剪贴板操作的文本。")
