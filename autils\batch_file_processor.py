import os
import logging
from pathlib import Path
# from abc import ABC # This import is no longer needed here if FileHandler is imported

from autils.file_handle_helper import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ReplaceHandler, CopyOnlyHandler, \
    MarkdownIdUpdateHandler, InPlaceFileHandler


# Removed duplicate FileHandler class definition here

def process_and_write_file(in_file_path: Path, out_file_path: Path, handlers: list):
    """
    公共辅助函数：处理文件内容并写入到目标路径。
    接收 Path 对象作为文件路径，并处理内容。
    """
    is_text = False
    file_content = None
    try:
        # 尝试以文本模式读取
        file_content = in_file_path.read_text(encoding='utf-8')
        is_text = True
    except UnicodeDecodeError:
        # 如果是二进制文件，则以二进制模式读取
        file_content = in_file_path.read_bytes()
        is_text = False
    except IOError as e:
        logging.error(f"无法读取文件 {in_file_path}: {e}")
        raise # 重新抛出异常，让上层调用者处理

    processed_content = file_content # 初始化为原始内容

    for handler in handlers:
        try:
            if is_text:
                processed_content = handler.process_text_content(in_file_path, processed_content)
            else:
                processed_content = handler.process_binary_content(in_file_path, processed_content)
        except Exception as e:
            logging.error(f"处理文件内容失败 (Handler: {handler.__class__.__name__}, File: {in_file_path}): {e}")
            # 可以选择跳过此文件或继续
            continue
    
    out_file_path.parent.mkdir(parents=True, exist_ok=True)

    try:
        if is_text:
            out_file_path.write_text(processed_content, encoding='utf-8')
        else:
            out_file_path.write_bytes(processed_content)
        logging.info(f"文件处理完成并保存到: {out_file_path}")
    except IOError as e:
        logging.error(f"无法写入文件 {out_file_path}: {e}")
        raise # 重新抛出异常，让上层调用者处理


class BatchFileProcessor:
    def __init__(self, in_dir: str, out_dir: str):
        self.in_dir = Path(in_dir)
        self.out_dir = Path(out_dir)
        self.is_in_place = (self.in_dir.resolve() == self.out_dir.resolve())

    def process(self, handlers: list = None, recursive: bool = True):
        if not self.in_dir.exists():
            logging.error(f"输入目录不存在: {self.in_dir}")
            return

        # 检查是否为原地更新模式
        if self.is_in_place:
            logging.info(f"检测到原地更新模式: {self.in_dir}")
            return self._process_in_place(handlers, recursive)
        else:
            if not self.out_dir.exists():
                self.out_dir.mkdir(parents=True)
                logging.info(f"创建输出目录: {self.out_dir}")
            return self._process_normal(handlers, recursive)

    def _process_normal(self, handlers: list = None, recursive: bool = True):
        """正常的复制模式处理"""
        effective_handlers = []
        if handlers:
            for handler in handlers:
                if isinstance(handler, FileHandler):
                    effective_handlers.append(handler)
                else:
                    logging.warning(f"警告: {handler.__class__.__name__} 不是 FileHandler 的实例，将被忽略。")

        total_files = 0
        processed_files_success = 0
        processed_files_failed = 0

        # 更高效地处理 recursive 参数
        for root, dirs, files in os.walk(self.in_dir, topdown=True):
            if not recursive:
                dirs[:] = []  # 阻止os.walk进入子目录

            current_root_path = Path(root)

            for file_name in files:
                total_files += 1
                # 移除文件扩展名过滤，让所有文件都经过handler处理
                in_file_full_path = current_root_path / file_name
                relative_path_from_in_dir = in_file_full_path.relative_to(self.in_dir)
                logging.info(f"正在处理文件: {relative_path_from_in_dir}")

                # Step 1: Process file path
                current_relative_path = str(relative_path_from_in_dir)
                path_processing_failed = False
                for handler in effective_handlers:
                    try:
                        current_relative_path = handler.process_file_path(current_relative_path)
                    except Exception as e:
                        logging.error(f"处理文件路径失败 (Handler: {handler.__class__.__name__}, File: {relative_path_from_in_dir}): {e}")
                        path_processing_failed = True
                        processed_files_failed += 1
                        break # 跳过当前文件的内容处理
                
                if path_processing_failed:
                    continue

                # Step 2: Determine final output path
                final_output_full_path = self.out_dir / current_relative_path

                # Step 3 & 4: Process content and write (delegated to helper)
                try:
                    process_and_write_file(in_file_full_path, final_output_full_path, effective_handlers)
                    processed_files_success += 1
                except (IOError, Exception) as e:
                    logging.error(f"处理或写入文件失败 (File: {in_file_full_path}): {e}")
                    processed_files_failed += 1
                    continue
            
            # recursive=False 的逻辑已经通过 dirs[:] = [] 优化，这里不再需要 break
            # if not recursive:
            #     break

        logging.info(f"\n--- 批量文件处理统计 ---")
        logging.info(f"总文件数: {total_files}")
        logging.info(f"成功处理文件数: {processed_files_success}")
        logging.info(f"处理失败文件数: {processed_files_failed}")
        logging.info(f"-----------------------\n")

    def _process_in_place(self, handlers: list = None, recursive: bool = True):
        """原地更新模式处理"""
        effective_handlers = []
        if handlers:
            for handler in handlers:
                if isinstance(handler, FileHandler):
                    effective_handlers.append(handler)
                else:
                    logging.warning(f"警告: {handler.__class__.__name__} 不是 FileHandler 的实例，将被忽略。")

        # 创建原地更新处理器
        in_place_handler = InPlaceFileHandler()

        total_files = 0
        processed_files_success = 0
        processed_files_failed = 0

        # 更高效地处理 recursive 参数
        for root, dirs, files in os.walk(self.in_dir, topdown=True):
            if not recursive:
                dirs[:] = []  # 阻止os.walk进入子目录

            current_root_path = Path(root)

            for file_name in files:
                total_files += 1
                in_file_full_path = current_root_path / file_name
                relative_path_from_in_dir = in_file_full_path.relative_to(self.in_dir)
                logging.info(f"正在原地处理文件: {relative_path_from_in_dir}")

                try:
                    # 使用原地更新处理器
                    success = in_place_handler.process_file_in_place(in_file_full_path, effective_handlers)
                    if success:
                        processed_files_success += 1
                    else:
                        processed_files_failed += 1
                except Exception as e:
                    logging.error(f"原地处理文件失败 (File: {in_file_full_path}): {e}")
                    processed_files_failed += 1
                    continue

        logging.info(f"\n--- 原地更新处理统计 ---")
        logging.info(f"总文件数: {total_files}")
        logging.info(f"成功处理文件数: {processed_files_success}")
        logging.info(f"处理失败文件数: {processed_files_failed}")
        logging.info(f"-----------------------\n")


class SingleFileProcessor:
    """
    单文件处理器，用于对单个输入文件应用一系列FileHandler，并将结果输出到指定的单个文件。
    方便测试FileHandler的效果或进行针对性处理。
    """
    def __init__(self, in_file_path: str, out_file_path: str):
        self.in_file_path = Path(in_file_path)
        self.out_file_path = Path(out_file_path)

    def process(self, handlers: list = None):
        if not self.in_file_path.exists():
            logging.error(f"输入文件不存在: {self.in_file_path}")
            return

        effective_handlers = []
        if handlers:
            for handler in handlers:
                if isinstance(handler, FileHandler):
                    effective_handlers.append(handler)
                else:
                    logging.warning(f"警告: {handler.__class__.__name__} 不是 FileHandler 的实例，将被忽略。")
        
        logging.info(f"正在处理单个文件: {self.in_file_path}")

        # Step 1: Process path for output basename
        final_output_basename = self.out_file_path.name
        for handler in effective_handlers:
            try:
                # 传递当前输出文件的basename给handler，让其修改
                final_output_basename = handler.process_file_path(final_output_basename)
            except Exception as e:
                logging.error(f"处理输出文件名失败 (Handler: {handler.__class__.__name__}, File: {self.out_file_path}): {e}")
                continue
        
        final_output_full_path = self.out_file_path.parent / final_output_basename

        # Step 2 & 3: Process content and write (delegated to helper)
        try:
            process_and_write_file(self.in_file_path, final_output_full_path, effective_handlers)
        except (IOError, Exception) as e:
            logging.error(f"无法读取/写入文件 {self.in_file_path} 或 {final_output_full_path}: {e}")
            return


# 以下是您的测试代码，您可以在这里添加SingleFileProcessor的测试示例
def test_批量文件处理器():
    in_dir = r'C:\Users\<USER>\Desktop\test_20_jkt_ops\test_90_tools\test_20_批量文件处理器'
    out_dir = r'C:\Users\<USER>\Desktop\test_20_jkt_ops\test_90_tools\test_20_批量文件处理器\\out'

    # 创建处理程序实例
    rename_handler = RenameHandler()
    replace_handler = ReplaceHandler()
    copy_only_handler = CopyOnlyHandler() # 新增的处理器
    markdown_id_handler = MarkdownIdUpdateHandler() # 新增的处理器

    # 将处理程序传递给 BatchFileProcessor 的 process 方法
    bfp = BatchFileProcessor(in_dir, out_dir)
    
    # 示例1: 仅重命名 .md 文件
    logging.info("\n--- 示例1: 仅重命名 .md 文件 ---")
    bfp.process(handlers=[rename_handler], recursive=True)

    # 示例2: 替换 .txt 文件内容
    logging.info("\n--- 示例2: 替换 .txt 文件内容 ---")
    # 假设in_dir中也有.txt文件，或者上一步已经将.md改为.txt
    bfp.process(handlers=[replace_handler], recursive=False) 

    # 示例3: 复制所有文件，不修改 (使用新增加的CopyOnlyHandler)
    logging.info("\n--- 示例3: 复制所有文件，不修改 ---")
    # 假设in_dir中也有.txt文件，或者上一步已经将.md改为.txt
    bfp.process(handlers=[copy_only_handler], recursive=True)

    # 示例4: 重命名并替换 .md 文件内容
    logging.info("\n--- 示例4: 重命名并替换 .md 文件内容 ---")
    bfp.process(handlers=[rename_handler, replace_handler], recursive=True)

    # 示例5: 更新Markdown文件ID
    logging.info("\n--- 示例5: 更新Markdown文件ID ---")
    bfp.process(handlers=[markdown_id_handler], recursive=True)

# 示例：如何在另一个测试文件中使用 SingleFileProcessor
# 假设您的 test_11_图片标准化.py 需要使用它
# from autils.batch_file_processor import SingleFileProcessor, MarkdownImageToHtmlHandler

# def test_single_file_image_conversion():
#     in_file = r'C:\Users\<USER>\Desktop\test_20_jkt_ops\test_90_tools\test_11_图片标准化\test_single.md'
#     out_file = r'C:\Users\<USER>\Desktop\test_20_jkt_ops\test_90_tools\test_11_图片标准化\out\test_single_converted.html'

#     # 确保 in_file 存在并包含测试内容
#     # 例如：在 test_single.md 中写入：这是一个![](image.jpg)文件
#     if not os.path.exists(os.path.dirname(in_file)):
#         os.makedirs(os.path.dirname(in_file))
#     with open(in_file, 'w', encoding='utf-8') as f:
#         f.write("这是一个![](test_image.jpg)文件。")

#     image_converter = MarkdownImageToHtmlHandler()
#     sfp = SingleFileProcessor(in_file_path=in_file, out_file_path=out_file)
#     sfp.process(handlers=[image_converter])

#     # 验证输出
#     with open(out_file, 'r', encoding='utf-8') as f:
#         content = f.read()
#     assert '<img src="test_image.jpg">' in content
#     logging.info(f"单文件转换测试通过：{out_file}")
