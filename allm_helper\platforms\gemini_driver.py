import logging
import time
from typing import Optional, List

from playwright.sync_api import Page, expect

from allm_helper.platforms.base_platform import BasePlatformDriver

logger = logging.getLogger(__name__)


class GeminiDriver(BasePlatformDriver):
    """
    Google Gemini 平台的具体驱动实现。
    """
    CHAT_URL = "https://gemini.google.com/app"

    def __init__(self, page: Page):
        """
        初始化 Gemini 驱动。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)
        # 等待页面加载完成，确保输入框可见
        try:
            self.page.wait_for_selector("div.input-area", timeout=20000)
        except Exception:
            logger.warning("Gemini 页面加载超时或未找到输入区域。")

    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建新会话。
        """
        logger.info("正在 Gemini 中创建新会话...")
        try:
            # 点击"新建聊天"按钮
            new_chat_button = self.page.locator('mat-icon[data-mat-icon-name="edit_square"]')
            if new_chat_button.is_enabled():
                new_chat_button.click()
            else:
                logger.info("'新建聊天'按钮不可用，可能已经是新会话。")
            # 等待新会话页面加载完成
            expect(self.page.locator('div.input-area')).to_be_visible(timeout=10000)
            logger.info("已成功创建新会话。")
        except Exception as e:
            logger.error(f"创建新会话失败: {e}")
            # 如果失败，尝试通过刷新页面来恢复
            self.page.reload()
            self.page.wait_for_selector("div.input-area", timeout=20000)
        return self


    def use_existing_conversation(self, conversation_title: str):
        """
        切换到已有会话。
        Gemini 的 Web UI 不容易直接通过标题切换会话，此功能暂不实现。
        """
        logger.warning(f"Gemini 驱动暂不支持按标题 '{conversation_title}' 切换会话。")
        pass

    def chat(self, prompt: str, attachments: Optional[List[str]] = None,
             response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。
        """
        self.switch_to_page()

        if not self.is_ready():
            raise Exception("平台正忙，无法发送新消息。")

        # 1. 上传附件 (如果需要)
        if attachments:
            logger.info(f"正在上传 {len(attachments)} 个文件...")
            try:
                # 点击上传按钮
                self.page.locator('button[aria-label*="Upload file"]').click()
                with self.page.expect_file_chooser() as fc_info:
                    self.page.locator('div[role="button"]:has-text("Upload from computer")').click()
                file_chooser = fc_info.value
                file_chooser.set_files(attachments)
                # 等待文件上传完成的某种指示，这里用短暂延时替代
                time.sleep(3)
                logger.info("文件上传完成。")
            except Exception as e:
                logger.error(f"上传文件失败: {e}")
                raise

        # 2. 输入提示并发送
        logger.info("正在发送提示...")
        try:
            input_area = self.page.locator('div.input-area')
            rich_text_editor = input_area.locator('[role="textbox"]')
            rich_text_editor.fill(prompt)
            # 点击发送按钮
            send_button = self.page.locator('[data-test-id="send-arrow"]')
            send_button.click()
            logger.info("提示已发送。")
        except Exception as e:
            logger.error(f"发送提示失败: {e}")
            raise

        # 3. 等待回复完成
        logger.info("正在等待 Gemini 回复...")
        try:
            # 等待生成停止的标志，例如"重做"按钮的出现
            expect(self.page.locator('response-container').last).to_be_visible(timeout=300000)
            # 等待分享和更多按钮组的出现，这是一个更可靠的结束标志
            expect(self.page.locator('.model-response-action-buttons').last).to_be_visible(timeout=300000)
            logger.info("Gemini 回复已接收。")
        except Exception as e:
            logger.error(f"等待回复超时或失败: {e}")
            # 尝试获取当前已生成的内容
            pass
            
        # 4. 获取回复内容
        logger.info("正在提取回复内容...")
        response_elements = self.page.locator('.response-container-content > .response-content').last.locator(".model-response-text")
        # 提取所有匹配元素的文本内容
        full_response = "\n".join([element.inner_text() for element in response_elements.all()])
        
        return full_response.strip()


    def is_ready(self) -> bool:
        """
        检查平台是否准备好接收新消息。
        通过检查发送按钮是否可见且可用。
        """
        try:
            send_button = self.page.locator('[data-test-id="send-arrow"]')
            return send_button.is_enabled()
        except Exception:
            return False

    def save_chat(self, chat_name: str):
        """
        保存当前会话并命名。
        Gemini 的 Web UI 不支持直接命名会话，此功能暂不实现。
        """
        logger.warning(f"Gemini 驱动暂不支持将会话保存为 '{chat_name}'。")
        pass

    def del_chat(self, chat_name: str):
        """
        删除指定名称的会话。
        Gemini 的 Web UI 不支持按名称删除会话，此功能暂不实现。
        """
        logger.warning(f"Gemini 驱动暂不支持删除名为 '{chat_name}' 的会话。")
        pass 