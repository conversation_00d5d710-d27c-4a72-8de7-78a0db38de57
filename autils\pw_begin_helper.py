import os

import pathlib

import pytest
from playwright.sync_api import sync_playwright

from autils.pw_login_helper import PLATFORM_CONFIG, handle_login_check


@pytest.fixture(scope="session")
def browser(cfg):
    # 启动浏览器 (此部分无改动)
    with sync_playwright() as p:
        browser = p.chromium.launch(**cfg.pw_launch_conf if cfg.pw_launch_conf else {})
        yield browser
        browser.close()


@pytest.fixture(scope="session")
def get_user_page(browser, cfg):
    contexts = []
    storage_info = {}  # 用于记录每个上下文对应的存储路径和配置
    # 确保目录存在
    pathlib.Path("./data/storage_state").mkdir(parents=True, exist_ok=True)

    def _get_user_page(platform, user, autosave=True):
        platform_config = PLATFORM_CONFIG.get(platform)
        if not platform_config:
            raise ValueError(f"平台 '{platform}' 未在 PLATFORM_CONFIG 中配置。")

        storage_path = f"./data/storage_state/{platform}-{user}.json"

        context = browser.new_context(
            storage_state=storage_path if os.path.exists(storage_path) else None,
            **cfg.pw_new_context_conf if cfg.pw_new_context_conf else {}
        )
        page = context.new_page()

        # 3. 调用封装好的登录处理函数
        handle_login_check(page, context, platform, user, platform_config, storage_path)

        storage_info[context] = {"storage_path": storage_path, "autosave": autosave}
        contexts.append(context)
        return page

    yield _get_user_page

    # 清理资源前保存状态
    print("\n所有测试已完成，正在清理会话并保存状态...")
    for context in contexts:
        info = storage_info.get(context)
        if info and info["autosave"]:
            try:
                # 修正了之前代码中的一个BUG，正确传入路径字符串
                context.storage_state(path=info["storage_path"])
                print(f"已更新状态文件: {info['storage_path']}")
            except Exception as e:
                print(f"保存状态文件 {info['storage_path']} 时出错: {e}")
        context.close()
