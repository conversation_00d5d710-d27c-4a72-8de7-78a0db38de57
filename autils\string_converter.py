from abc import ABC, abstractmethod
from typing import List, Dict, Optional
import re


class ConversionRule(ABC):
    """转换规则基类"""
    
    @abstractmethod
    def convert(self, text: str) -> str:
        """执行转换"""
        pass


class TextTransformer(ConversionRule):
    """文本替换转换器"""
    
    def __init__(self, 
                 regex_mappings: Optional[Dict[str, str]] = None,
                 replacement_mappings: Optional[Dict[str, str]] = None
                 ):
        """
        初始化文本替换转换器
        
        Args:
            regex_mappings: 正则表达式替换映射，如 {r"(\d+)\. ": r"\1-"}
            replacement_mappings: 字符串替换映射，如 {". ": "-", " ": "-"}
        """
        self.replacement_mappings = replacement_mappings or {}
        self.regex_mappings = regex_mappings or {}
    
    def convert(self, text: str) -> str:
        """执行文本替换转换"""
        result = text

        # 应用正则表达式替换
        for pattern, replacement in self.regex_mappings.items():
            result = re.sub(pattern, replacement, result)

        # 应用普通字符串替换
        for old, new in self.replacement_mappings.items():
            result = result.replace(old, new)



        return result


class PathTransformer(ConversionRule):
    """路径转换器"""

    def __init__(self,
                 path_separator: str = "/",
                 exact_mappings: Optional[Dict[str, str]] = None,
                 replacement_mappings: Optional[Dict[str, str]] = None,
                 delete_patterns: Optional[List[str]] = None):
        """
        初始化路径转换器

        Args:
            path_separator: 路径分隔符
            exact_mappings: 完整段落匹配映射，如 {"考试科目1 系统架构设计综合知识": "综合知识"}
            replacement_mappings: 字符串替换映射，如 {". ": "-", " ": "-"}
            delete_patterns: 需要删除的完整匹配模式列表，如 ["考试科目1", "考试科目2"]
        """
        self.path_separator = path_separator
        self.exact_mappings = exact_mappings or {}
        self.replacement_mappings = replacement_mappings or {}
        self.delete_patterns = delete_patterns or []

    def convert(self, text: str) -> str:
        """执行路径转换"""
        # 分割路径
        parts = text.split(self.path_separator)

        # 转换每个部分
        converted_parts = []
        for part in parts:
            # 1. 检查是否需要删除
            if part in self.delete_patterns:
                continue

            # 2. 应用完整段落匹配规则
            if part in self.exact_mappings:
                part = self.exact_mappings[part]
            else:
                # 3. 应用字符串替换规则
                for old, new in self.replacement_mappings.items():
                    part = part.replace(old, new)

            converted_parts.append(part)

        # 重新组合路径
        return self.path_separator.join(converted_parts)


class StringConverter:
    """字符串转换器"""

    def __init__(self):
        """初始化转换器"""
        self.rules: List[ConversionRule] = []

    def add_rule(self, rule: ConversionRule) -> None:
        """
        添加转换规则

        Args:
            rule: 转换规则实例
        """
        self.rules.append(rule)

    def convert(self, text: str) -> str:
        """
        执行转换

        Args:
            text: 要转换的文本

        Returns:
            str: 转换后的文本
        """
        result = text
        for rule in self.rules:
            result = rule.convert(result)
        return result


def test_path_conversion():
    """测试路径转换"""
    # 创建转换器
    converter = StringConverter()

    # 创建文本替换规则
    text_rule = TextTransformer(
        regex_mappings={
            r"(\d+)\. ": r"\1-",  # 匹配一级标题，如 "8. " -> "8-"
            r"(\d+(?:\.\d+)+) ": r"\1-",  # 匹配多级标题，如 "8.1 " -> "8.1-", "8.1.1 " -> "8.1.1-"
        },
        replacement_mappings={
            " ": ""
        },
    )

    # 创建路径转换规则
    path_rule = PathTransformer(
        exact_mappings={
            "考试科目1 系统架构设计综合知识": "综合知识"
        },
        replacement_mappings={
            " ": ""
        },
        delete_patterns=[
            "考试科目1",
            "考试科目2"
        ]
    )

    # 添加规则
    converter.add_rule(text_rule)
    converter.add_rule(path_rule)

    # 测试转换
    test_path = "考试科目1 系统架构设计综合知识/8. 计算机系统基本知识/8.1 计算机硬件/8.1.1 硬件基础/8.1.1.1 基础概念"
    converted = converter.convert(test_path)
    print(f"原始路径: {test_path}")
    print(f"转换后: {converted}")

    # 测试其他路径
    test_path2 = "考试科目1 系统架构设计综合知识/8. 信息系统基础知识/8.1 信息系统概述/8.1.1 系统架构/8.1.1.1 架构设计"
    converted2 = converter.convert(test_path2)
    print(f"\n原始路径: {test_path2}")
    print(f"转换后: {converted2}")

    # 测试删除规则
    test_path3 = "考试科目1/综合知识/8. 计算机系统基本知识/8.1 基础知识/8.1.1 硬件/8.1.1.1 CPU"
    converted3 = converter.convert(test_path3)
    print(f"\n原始路径: {test_path3}")
    print(f"转换后: {converted3}")

    # 测试纯文本替换
    test_text = "8. 这是一个测试文本 8.1 包含多个空格 8.1.1 测试正则 8.1.1.1 四级标题"
    converted4 = text_rule.convert(test_text)
    print(f"\n原始文本: {test_text}")
    print(f"转换后: {converted4}")


if __name__ == "__main__":
    test_path_conversion() 