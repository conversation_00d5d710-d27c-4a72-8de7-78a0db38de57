from typing import Dict, Optional, Tuple, Union, Any, List, Iterator
import re
from pathlib import Path
import yaml

class FrontMatter:
    """处理 Markdown 文件的 front matter"""
    
    def __init__(self, content: str):
        self.content = content
        self._data: Optional[Dict] = None
        self._raw_text: Optional[str] = None
        
    def extract(self) -> Dict:
        """提取 front matter 内容"""
        if self._data is not None:
            return self._data
            
        pattern = r'^---\s*\n(.*?)\n---\s*\n'
        match = re.match(pattern, self.content, re.DOTALL)
        
        if not match:
            self._data = {}
            self._raw_text = ""
            return self._data
            
        self._raw_text = match.group(1)
        try:
            self._data = yaml.safe_load(self._raw_text) or {}
        except yaml.YAMLError:
            self._data = {}
            
        return self._data
        
    def update(self, data: Dict[str, Any]) -> None:
        """更新 front matter 内容"""
        current_data = self.extract()
        current_data.update(data)
        self._data = current_data
        
    def remove(self, keys: Union[str, list[str]]) -> None:
        """删除指定的 front matter 键"""
        if isinstance(keys, str):
            keys = [keys]
        current_data = self.extract()
        for key in keys:
            current_data.pop(key, None)
        self._data = current_data
        
    def to_string(self) -> str:
        """将 front matter 转换为字符串"""
        if not self._data:
            return ""
        return yaml.dump(self._data, allow_unicode=True, sort_keys=False)

class MarkdownHelper:
    """Markdown 文件助手类，用于处理 Markdown 文件的基本操作"""
    
    def __init__(self, content_or_path: Union[str, Path]):
        """
        初始化 Markdown 助手
        
        Args:
            content_or_path: Markdown 文件内容或文件路径
        """
        if isinstance(content_or_path, (str, Path)) and Path(content_or_path).is_file():
            self.file_path = Path(content_or_path)
            self.content = self.file_path.read_text(encoding='utf-8')
        else:
            self.file_path = None
            self.content = str(content_or_path)
            
        self._front_matter = FrontMatter(self.content)
        self._body_content: Optional[str] = None
        
    @classmethod
    def from_file(cls, file_path: Union[str, Path]) -> 'MarkdownHelper':
        """从文件创建 Markdown 助手实例"""
        return cls(file_path)
        
    @classmethod
    def from_content(cls, content: str) -> 'MarkdownHelper':
        """从内容创建 Markdown 助手实例"""
        return cls(content)
        
    def extract_front_matter(self) -> Dict:
        """提取 front matter 内容"""
        return self._front_matter.extract()
        
    def update_front_matter(self, data: Dict[str, Any]) -> None:
        """更新 front matter 内容"""
        self._front_matter.update(data)
        
    def remove_front_matter_keys(self, keys: Union[str, list[str]]) -> None:
        """删除指定的 front matter 键"""
        self._front_matter.remove(keys)
        
    def extract_content(self) -> str:
        """提取主要内容文本"""
        if self._body_content is not None:
            return self._body_content
            
        pattern = r'^---\s*\n.*?\n---\s*\n'
        content = re.sub(pattern, '', self.content, flags=re.DOTALL)
        self._body_content = content.strip()
        return self._body_content
        
    def save(self, file_path: Optional[Union[str, Path]] = None) -> None:
        """保存修改后的内容到文件"""
        if file_path is None:
            if self.file_path is None:
                raise ValueError("No file path specified")
            file_path = self.file_path
            
        file_path = Path(file_path)
        front_matter_str = self._front_matter.to_string()
        content = self.extract_content()
        
        if front_matter_str:
            new_content = f"---\n{front_matter_str}---\n\n{content}"
        else:
            new_content = content
            
        file_path.write_text(new_content, encoding='utf-8')

class MultiMarkdownHelper:
    """处理包含多个独立 Markdown 文本的文件"""
    
    SPLIT_MARKER = "---split---"
    
    def __init__(self, content_or_path: Union[str, Path]):
        """
        初始化多段 Markdown 助手
        
        Args:
            content_or_path: 包含多个 Markdown 文本的文件内容或文件路径
        """
        if isinstance(content_or_path, (str, Path)) and Path(content_or_path).is_file():
            self.file_path = Path(content_or_path)
            self.content = self.file_path.read_text(encoding='utf-8')
        else:
            self.file_path = None
            self.content = str(content_or_path)
            
        self._helpers: List[MarkdownHelper] = []
        self._parse_content()
        
    def _parse_content(self) -> None:
        """解析内容，创建多个 MarkdownHelper 实例"""
        sections = self.content.split(self.SPLIT_MARKER)
        self._helpers = [MarkdownHelper(section.strip()) for section in sections if section.strip()]
        
    @classmethod
    def from_file(cls, file_path: Union[str, Path]) -> 'MultiMarkdownHelper':
        """从文件创建多段 Markdown 助手实例"""
        return cls(file_path)
        
    @classmethod
    def from_content(cls, content: str) -> 'MultiMarkdownHelper':
        """从内容创建多段 Markdown 助手实例"""
        return cls(content)
        
    def __iter__(self) -> Iterator[MarkdownHelper]:
        """迭代所有 Markdown 段落"""
        return iter(self._helpers)
        
    def __len__(self) -> int:
        """返回 Markdown 段落数量"""
        return len(self._helpers)
        
    def __getitem__(self, index: int) -> MarkdownHelper:
        """通过索引获取特定的 Markdown 段落"""
        return self._helpers[index]
        
    def save(self, file_path: Optional[Union[str, Path]] = None) -> None:
        """保存所有修改后的内容到文件"""
        if file_path is None:
            if self.file_path is None:
                raise ValueError("No file path specified")
            file_path = self.file_path
            
        file_path = Path(file_path)
        contents = []
        
        for helper in self._helpers:
            front_matter_str = helper._front_matter.to_string()
            content = helper.extract_content()
            
            if front_matter_str:
                section_content = f"---\n{front_matter_str}---\n\n{content}"
            else:
                section_content = content
                
            contents.append(section_content)
            
        file_path.write_text(f"\n\n{self.SPLIT_MARKER}\n\n".join(contents), encoding='utf-8')
