# AI Studio 测试用例说明

本目录包含针对 `aistudio_driver.py` 的全面测试用例。

## 测试用例概述

### 基础功能测试
- `test_basic_chat()`: 测试基本聊天功能
- `test_system_prompt()`: 测试系统提示词功能
- `test_model_configuration()`: 测试不同模型配置
- `test_temperature_settings()`: 测试温度参数设置

### 对话管理测试
- `test_long_prompt()`: 测试长提示词处理
- `test_multiple_messages()`: 测试多轮对话
- `test_response_formats()`: 测试不同响应格式

### 平台功能测试
- `test_is_ready_status()`: 测试平台就绪状态检查
- `test_save_chat()`: 测试保存会话功能
- `test_use_existing_conversation()`: 测试使用已有会话
- `test_file_upload()`: 测试文件上传功能

### 高级功能测试
- `test_autosave_functionality()`: 测试自动保存功能
- `test_thinking_mode()`: 测试思考模式功能

### 错误处理测试
- `test_error_handling()`: 测试错误处理机制
- `test_del_chat_warning()`: 测试删除会话功能（应显示警告）

### 综合测试
- `test_comprehensive_workflow()`: 测试完整的工作流程
- `test_1()`: 原始基础测试（保持向后兼容）

## 运行测试

从项目根目录运行：

```bash
# 运行所有 aistudio 测试
G:/yjxt2025/bpllm/venv/Scripts/python.exe -m pytest test_llms/test_aistudio/test_1.py -v

# 运行特定测试
G:/yjxt2025/bpllm/venv/Scripts/python.exe -m pytest test_llms/test_aistudio/test_1.py::test_basic_chat -v

# 运行测试并显示详细输出
G:/yjxt2025/bpllm/venv/Scripts/python.exe -m pytest test_llms/test_aistudio/test_1.py -v -s
```

## 测试要求

1. **环境要求**：
   - 需要有效的 Google 账号登录状态
   - 需要网络连接访问 AI Studio
   - 需要浏览器环境（通过 Playwright）

2. **配置要求**：
   - 用户邮箱配置为 "<EMAIL>"
   - 默认模型为 "Gemini 2.5 Flash"

3. **注意事项**：
   - 某些测试可能因网络问题或平台限制而失败，这是正常的
   - 文件上传测试会创建临时文件并自动清理
   - 会话保存功能依赖于平台的实际可用性

## 测试覆盖的功能

### AistudioDriver 类的主要方法
- `__init__()`: 初始化和配置
- `new_conversation()`: 创建新会话
- `chat()`: 发送消息和接收回复
- `save_chat()`: 保存会话
- `use_existing_conversation()`: 使用已有会话
- `is_ready()`: 检查平台状态
- `autosave()`: 自动保存设置
- `thinking_mode()`: 思考模式设置
- `del_chat()`: 删除会话（显示不支持警告）

### 私有方法（间接测试）
- `_apply_conversation_settings()`: 通过配置测试间接验证
- `_upload_files()`: 通过文件上传测试验证
- `_wait_chat_finished()`: 通过聊天测试验证
- `_get_reply()`: 通过聊天测试验证

## 预期结果

所有测试通过后，可以确保：
1. AI Studio 驱动的基本功能正常工作
2. 配置参数能够正确应用
3. 错误处理机制有效
4. 文件上传功能可用
5. 会话管理功能正常
6. 高级功能（自动保存、思考模式）可用

## 故障排除

如果测试失败，请检查：
1. 网络连接是否正常
2. Google 账号是否已登录
3. AI Studio 平台是否可访问
4. 浏览器环境是否正确配置
5. 虚拟环境是否激活
