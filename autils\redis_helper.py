import json
import pytest
import redis
import uuid


def publish_task(redis_obj, queue_name, task_data):
    redis_obj.lpush(queue_name, json.dumps(task_data, ensure_ascii=False))


def consume_task(redis_obj, queue_name):
    res = redis_obj.brpop(queue_name, timeout=10)
    if not res:
        return None
    return json.loads(res[1]) # 0为queue_name


def publish_task_and_wait_for_result(redis_obj, queue_name, task_data, timeout=30):
    """
    发布一个任务并阻塞等待结果。

    :param redis_obj: Redis connection object.
    :param queue_name: The name of the queue to publish the task to.
    :param task_data: The data for the task.
    :param timeout: How long to wait for the result in seconds.
    :return: The result of the task, or None if it times out.
    """
    reply_to = f"result:{uuid.uuid4()}"
    task_data_with_reply = {
        "payload": task_data,
        "reply_to": reply_to
    }
    publish_task(redis_obj, queue_name, task_data_with_reply)

    # Block and wait for the result
    res = redis_obj.brpop(reply_to, timeout=timeout)
    if not res:
        return None  # Timeout
    return json.loads(res[1])


def process_one_task(redis_obj, queue_name, handler_func, wait_timeout=None):
    """
    封装了worker的行为: 等待一个任务，用给定的处理器执行它，然后把结果发回。

    :param redis_obj: Redis connection object.
    :param queue_name: The name of the queue to wait for tasks on.
    :param handler_func: The function to call to process the task payload.
    :param wait_timeout: How long to wait for a task in seconds. If None (default), waits forever.
    """
    timeout = wait_timeout if wait_timeout is not None else 0
    # brpop returns a tuple (queue_name, item) or None on timeout
    raw_task = redis_obj.brpop(queue_name, timeout=timeout)

    if not raw_task:
        print(f"Worker: 在超时内未在队列 '{queue_name}' 上收到任务。")
        return

    task_with_reply = json.loads(raw_task[1])
    payload = task_with_reply.get("payload")
    reply_to = task_with_reply.get("reply_to")

    print(f"Worker 收到任务: {payload}")
    # 使用提供的处理器处理任务
    result = handler_func(payload)
    print(f"Worker 完成处理，结果: {result}")

    # 将结果发送回去
    if reply_to:
        redis_obj.lpush(reply_to, json.dumps(result, ensure_ascii=False))
        print(f"Worker 已将结果发送到 {reply_to}")


# --- 这是被删除的危险方法示例 ---
def clear_all_tasks(redis_obj, queue_name):
    """
    (不推荐) 删除指定队列中的所有任务。这是一个破坏性操作！
    """
    print(f"警告：正在删除队列 '{queue_name}' 中的所有任务！")
    # DEL 命令会直接删除整个key
    redis_obj.delete(queue_name)

@pytest.fixture(scope="module")
def redis_obj(cfg):
    return redis.Redis(decode_responses=True, **cfg.redis_conf)
