import re

def remove_regex_suffix(text, pattern_str):
    """
    使用正则表达式删除文本末尾匹配的模式
    :param text: 原始文本
    :param pattern_str: 正则表达式模式（支持通配符）
    :return: 删除匹配内容后的文本
    """
    pattern = re.compile(pattern_str + r'\Z', re.DOTALL)
    return pattern.sub('', text)


def normalize_text(text):
    """标准化文本：半角转全角"""
    if not isinstance(text, str):
        return ""

    # 半角转全角映射
    mapping = {
        '(': '（',
        ')': '）',
        '/': '／',
        # '-': '－',
    }

    for k, v in mapping.items():
        text = text.replace(k, v)

    return text


