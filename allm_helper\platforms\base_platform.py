from abc import ABC, abstractmethod
from playwright.sync_api import Page, Browser
from typing import Optional, List


class BasePlatformDriver(ABC):
    """
    大模型平台驱动的抽象基类，定义统一的操作接口。
    
    重构后的接口设计：
    1. 初始化时只需要传入 page 对象
    2. 模型配置在 new_conversation 时传入
    """
    
    def __init__(self, page: Page):
        """
        初始化平台驱动，只接受 page 参数。

        Args:
            page: Playwright 页面对象
        """
        self.page = page

    def switch_to_page(self):
        """
        切换到当前驱动的页面。
        在执行chat等操作前应该调用此方法确保在正确的页面上。
        """
        # try:
        #     self.page.bring_to_front()
        # except Exception:
        #     # 如果页面已经在前台或其他原因导致失败，忽略错误
        #     pass
    
    @abstractmethod
    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建新会话，并应用模型配置。
        
        Args:
            model_config: 模型配置字典，包含模型名称、参数等
                - model_name: 模型名称
                - model_type: 模型类型（如适用）
                - temperature: 温度参数
                - top_p: Top-P 参数
                - system_prompt: 系统提示词
                - temp_chat: 是否使用临时聊天（Monica 特有）
                - 其他平台特定参数
        """
        pass
    
    @abstractmethod
    def use_existing_conversation(self, conversation_title: str):
        """
        切换到已有会话。
        
        Args:
            conversation_title: 会话标题
        """
        pass
    
    @abstractmethod
    def chat(self, prompt: str, attachments: Optional[List[str]] = None, 
             response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。
        
        Args:
            prompt: 用户输入的提示
            attachments: 要上传的附件的本地文件路径列表
            response_format: 响应格式，可选 "text"、"markdown"、"code" 等
            **kwargs: 其他平台特定参数
            
        Returns:
            AI的回复文本
        """
        pass
    
    @abstractmethod
    def is_ready(self) -> bool:
        """
        检查平台是否准备好接收新消息。
        
        Returns:
            True 如果平台准备就绪，False 否则
        """
        pass
    
    @abstractmethod
    def save_chat(self, chat_name: str):
        """
        保存当前会话并命名。
        
        Args:
            chat_name: 会话名称
        """
        pass
    
    @abstractmethod
    def del_chat(self, chat_name: str):
        """
        删除指定名称的会话。
        
        Args:
            chat_name: 要删除的会话名称
        """
        pass
