import io
import os
import tkinter as tk

from PIL import Image


def copy_image_to_clipboard(image_path):
    """将图片文件复制到系统剪切板"""
    try:
        # 创建tkinter根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口

        # 打开图片
        with Image.open(image_path) as img:
            # 将图片转换为BMP格式的字节流（Windows剪切板支持）
            output = io.BytesIO()
            img.save(output, format='BMP')
            bmp_data = output.getvalue()

            # 清空剪切板
            root.clipboard_clear()

            # 将BMP数据写入剪切板
            # 注意：tkinter的clipboard_append只支持文本，对于图片需要使用win32clipboard
            try:
                import win32clipboard
                from PIL import ImageGrab

                # 使用win32clipboard处理图片
                win32clipboard.OpenClipboard()
                win32clipboard.EmptyClipboard()

                # 将图片数据设置到剪切板
                # CF_DIB = 8 是设备无关位图格式
                win32clipboard.SetClipboardData(win32clipboard.CF_DIB, bmp_data[14:])  # 跳过BMP文件头
                win32clipboard.CloseClipboard()

            except ImportError:
                # 如果没有win32clipboard，使用PIL的ImageGrab（仅限Windows）
                # 先保存图片到临时位置，然后使用系统命令
                print("win32clipboard不可用，尝试使用替代方案...")

                # 使用PIL直接操作剪切板（如果支持）
                try:
                    # 这种方法在某些系统上可能不工作
                    img.save(output, format='BMP')
                    # 使用系统命令复制图片（Windows）
                    import subprocess
                    import tempfile

                    with tempfile.NamedTemporaryFile(suffix='.bmp', delete=False) as temp_bmp:
                        img.save(temp_bmp.name, 'BMP')

                        # 使用PowerShell命令复制图片到剪切板
                        cmd = f'''
                        Add-Type -AssemblyName System.Windows.Forms
                        $img = [System.Drawing.Image]::FromFile("{temp_bmp.name}")
                        [System.Windows.Forms.Clipboard]::SetImage($img)
                        $img.Dispose()
                        '''
                        subprocess.run(['powershell', '-Command', cmd], check=True)

                        # 清理临时文件
                        os.unlink(temp_bmp.name)

                except Exception as fallback_error:
                    print(f"替代方案也失败: {fallback_error}")
                    raise

        root.destroy()

    except Exception as e:
        print(f"复制图片到剪切板时出错: {e}")
        raise


def save_clipboard_image_to_file(output_path):
    """从系统剪切板获取图片并保存到文件"""
    try:
        # 尝试使用PIL的ImageGrab获取剪切板图片
        try:
            from PIL import ImageGrab

            # 从剪切板获取图片
            clipboard_image = ImageGrab.grabclipboard()

            if clipboard_image is not None:
                # 保存图片
                clipboard_image.save(output_path, 'PNG')
                return True
            else:
                print("剪切板中没有图片")
                return False

        except ImportError:
            print("PIL.ImageGrab不可用，尝试使用win32clipboard...")

            # 使用win32clipboard作为备选方案
            try:
                import win32clipboard
                from PIL import Image

                win32clipboard.OpenClipboard()

                # 检查剪切板中是否有图片数据
                if win32clipboard.IsClipboardFormatAvailable(win32clipboard.CF_DIB):
                    # 获取DIB数据
                    dib_data = win32clipboard.GetClipboardData(win32clipboard.CF_DIB)

                    # 构造BMP文件头
                    bmp_header = b'BM'  # BMP签名
                    file_size = len(dib_data) + 14  # 文件大小
                    reserved = b'\x00\x00\x00\x00'  # 保留字段
                    offset = b'\x36\x00\x00\x00'  # 数据偏移

                    # 组合完整的BMP数据
                    bmp_data = bmp_header + file_size.to_bytes(4, 'little') + reserved + offset + dib_data

                    # 使用PIL打开BMP数据
                    image = Image.open(io.BytesIO(bmp_data))
                    image.save(output_path, 'PNG')

                    win32clipboard.CloseClipboard()
                    return True
                else:
                    print("剪切板中没有图片格式的数据")
                    win32clipboard.CloseClipboard()
                    return False

            except ImportError:
                print("win32clipboard也不可用")
                return False
            except Exception as e:
                print(f"使用win32clipboard时出错: {e}")
                try:
                    win32clipboard.CloseClipboard()
                except:
                    pass
                return False

    except Exception as e:
        print(f"从剪切板获取图片时出错: {e}")
        return False
