import logging
import time
import textwrap
from typing import Optional, List
from playwright.sync_api import Page

from allm_helper.platforms.base_platform import BasePlatformDriver
from autils.pw_clipboard_helper import UniversalClipboardInterceptor

logger = logging.getLogger(__name__)


class YuanbaoDriver(BasePlatformDriver):
    """
    腾讯元宝平台的具体驱动实现。
    """
    CHAT_URL = "https://yuanbao.tencent.com/chat"

    def __init__(self, page: Page):
        """
        初始化腾讯元宝驱动，只接受 page 参数。
        模型配置将在 new_conversation 时传入。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)
        
        # 默认配置值
        self.current_model_config = {
            'system_prompt': '',
            'temperature': 0.7,
            'top_p': 0.9,
            'model_name': "混元大模型",  # 腾讯元宝默认模型
        }
        
        # 状态追踪
        self.first_upload = True
        self.support_system_prompt = False
        self.initial_prompt_prefix = ""
        
        # 超时设置
        self.upload_files_timeout = 10000
        self.wait_reply_timeout = 60000
        
        # 响应格式
        self.context_format = "text"

    def _apply_conversation_settings(self):
        """应用会话设置，如模型、温度、系统提示等"""
        try:
            # 如果有系统提示词但不支持系统提示，则添加到初始提示前缀
            system_prompt = textwrap.dedent(self.current_model_config.get('system_prompt', ''))
            if system_prompt:
                if self.support_system_prompt:
                    self._update_system_prompt(system_prompt)
                else:
                    self.initial_prompt_prefix = f"{system_prompt}\n\n问题：\n"
        except Exception as e:
            logger.warning(f"应用会话设置失败: {e}")

    def _update_system_prompt(self, system_prompt):
        """更新系统提示词（如果平台支持）"""
        # 腾讯元宝可能不支持系统提示词，这里预留接口
        logger.warning("腾讯元宝暂不支持系统提示词设置")
        pass

    def _scroll_to_bottom(self, scroll_container):
        """滚动到容器底部"""
        try:
            if scroll_container.is_visible():
                scroll_container.evaluate('''(element) => {
                    element.scrollTop = element.scrollHeight;
                    const event = new Event('scroll');
                    element.dispatchEvent(event);
                }''')
        except Exception as e:
            logger.debug(f"滚动失败: {e}")

    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建新会话，并应用模型配置。

        Args:
            model_config: 模型配置字典，包含：
                - model_name: 模型名称
                - temperature: 温度参数
                - top_p: Top-P 参数
                - system_prompt: 系统提示词
                - 其他腾讯元宝特定参数
        """
        try:
            # 尝试点击新建对话按钮
            # 这里需要根据实际的腾讯元宝界面来调整选择器
            new_chat_selectors = [
                'button[aria-label="新建对话"]',
                'button:has-text("新建对话")',
                '.new-chat-btn',
                '[data-testid="new-chat"]'
            ]
            
            for selector in new_chat_selectors:
                try:
                    self.page.locator(selector).click(timeout=3000)
                    logger.info("成功点击新建对话按钮")
                    break
                except:
                    continue
            else:
                logger.info("未找到新建对话按钮，可能已经在新对话页面")
            
            self.initial_prompt_prefix = ""
            
            # 更新当前模型配置
            if model_config:
                self.current_model_config.update(model_config)
            
            self._apply_conversation_settings()
            
        except Exception as e:
            logger.error(f"创建新会话失败: {e}")
            
        return self

    def use_existing_conversation(self, conversation_title: str):
        """使用已有会话"""
        logger.info(f"正在查找并切换到会话: '{conversation_title}'...")
        try:
            # 这里需要根据腾讯元宝的实际界面来实现
            # 通常需要在历史对话列表中搜索
            logger.warning("腾讯元宝的历史对话切换功能待实现")
        except Exception as e:
            logger.error(f"切换到已有会话失败: {e}")
            raise

    def _upload_files(self, files):
        """上传文件"""
        try:
            # 查找上传按钮
            upload_selectors = [
                'button[aria-label="上传文件"]',
                'input[type="file"]',
                '.upload-btn',
                '[data-testid="upload"]'
            ]
            
            for selector in upload_selectors:
                try:
                    if 'input[type="file"]' in selector:
                        # 直接设置文件
                        self.page.locator(selector).set_input_files(files)
                    else:
                        # 点击按钮触发文件选择
                        with self.page.expect_file_chooser() as fc_info:
                            self.page.locator(selector).click(timeout=3000)
                        file_chooser = fc_info.value
                        file_chooser.set_files(files)
                    
                    logger.info(f"成功上传 {len(files)} 个文件")
                    return True
                except:
                    continue
            
            logger.warning("未找到文件上传功能")
            return False
            
        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            return False

    def _wait_for_response_complete(self):
        """等待响应完成"""
        try:
            # 首先等待"停止回答"按钮消失，这表明AI已完成回复
            try:
                # 等待"停止回答"按钮出现（表明开始生成）
                self.page.wait_for_selector('text=停止回答', timeout=5000)
                logger.debug("检测到AI开始生成回复")

                # 然后等待"停止回答"按钮消失（表明生成完成）
                self.page.wait_for_selector('text=停止回答', state='hidden', timeout=self.wait_reply_timeout)
                logger.debug("检测到AI完成回复生成")

                # 额外等待一点时间确保内容完全渲染
                time.sleep(2)
                return

            except Exception:
                logger.debug("未检测到停止回答按钮，尝试其他方法")

            # 备用方法：等待响应完成的其他标志
            completion_selectors = [
                '.message-complete',
                '.response-done',
                'button[aria-label="复制"]',
                '.copy-btn'
            ]

            for selector in completion_selectors:
                try:
                    self.page.locator(selector).last.wait_for(state="visible", timeout=self.wait_reply_timeout)
                    logger.debug("检测到响应完成标志")
                    return
                except:
                    continue

            # 如果没有明确的完成标志，等待一段时间
            logger.debug("使用固定等待时间")
            time.sleep(5)

        except Exception as e:
            logger.warning(f"等待响应完成时出错: {e}")

    def _get_reply(self):
        """获取回复内容"""
        try:
            # 基于实际测试，腾讯元宝的回复结构包含思考过程和最终回复
            # 我们需要获取最终的回复内容，跳过思考过程

            # 等待AI回复出现
            self.page.wait_for_selector('p', timeout=30000)

            # 尝试获取最终回复内容（跳过思考过程）
            # 基于观察，最终回复通常在思考过程之后的paragraph元素中
            try:
                # 查找包含实际回复的段落（通常在思考过程后面）
                reply_paragraphs = self.page.locator('p').all()

                # 从后往前查找，跳过空段落和思考过程
                for i in range(len(reply_paragraphs) - 1, -1, -1):
                    paragraph = reply_paragraphs[i]
                    if paragraph.is_visible():
                        text = paragraph.inner_text().strip()
                        # 跳过空内容、输入提示、思考过程等
                        if (text and
                            text != "有问题，尽管问，shift+enter换行" and
                            not text.startswith("嗯，用户正在") and
                            not text.startswith("用户刻意强调") and
                            not text.startswith("考虑到这是") and
                            "思考中" not in text and
                            "已深度思考" not in text):
                            return text

                # 如果上述方法失败，尝试其他选择器
                reply_selectors = [
                    'p:last-of-type',  # 最后一个paragraph元素
                    '.message-content:last-child',
                    '.response-text:last-child',
                    '.chat-message:last-child .content',
                    '.assistant-message:last-child',
                    '[data-role="assistant"]:last-child'
                ]

                for selector in reply_selectors:
                    try:
                        reply_element = self.page.locator(selector).last
                        if reply_element.is_visible():
                            reply_text = reply_element.inner_text()
                            if reply_text.strip() and reply_text.strip() != "有问题，尽管问，shift+enter换行":
                                return reply_text.strip()
                    except:
                        continue

            except Exception as e:
                logger.debug(f"段落解析失败: {e}")

            # 如果上述方法都失败，尝试复制功能
            try:
                return self._get_reply_by_copy()
            except:
                pass

            logger.warning("无法获取回复内容")
            return "无法获取回复内容"

        except Exception as e:
            logger.error(f"获取回复失败: {e}")
            return f"获取回复失败: {e}"

    def _get_reply_by_copy(self):
        """通过复制功能获取回复"""
        try:
            # 查找复制按钮
            copy_selectors = [
                'button[aria-label="复制"]',
                '.copy-btn',
                'button:has-text("复制")',
                '[data-testid="copy"]'
            ]
            
            for selector in copy_selectors:
                try:
                    copy_btn = self.page.locator(selector).last
                    if copy_btn.is_visible():
                        with UniversalClipboardInterceptor(self.page) as interceptor:
                            copy_btn.click()
                            interceptor.wait_for_capture(timeout=5.0)
                            return interceptor.text
                except:
                    continue
            
            raise Exception("未找到复制按钮")
            
        except Exception as e:
            logger.error(f"通过复制获取回复失败: {e}")
            raise

    def chat(self, prompt: str, attachments: Optional[List[str]] = None, 
             response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。

        :param prompt: 用户输入的提示。
        :param attachments: 要上传的附件的本地文件路径列表。
        :param response_format: 响应格式，可选 "text"。
        :return: AI的回复文本。
        """
        # 首先切换到当前驱动的页面
        self.switch_to_page()

        if not self.is_ready():
            raise Exception("平台正忙，无法发送新消息。")

        try:
            # 如果有初始提示前缀（系统提示词），则拼接
            if self.initial_prompt_prefix:
                full_prompt = self.initial_prompt_prefix + prompt
                self.initial_prompt_prefix = ""  # 仅使用一次
            else:
                full_prompt = prompt

            # 1. 上传附件 (如果需要)
            if attachments:
                upload_success = self._upload_files(attachments)
                if not upload_success:
                    logger.warning("文件上传失败，继续发送文本消息")

            # 2. 查找输入框并输入提示 - 基于实际观察到的结构
            input_selectors = [
                '.ql-editor',  # 腾讯元宝实际使用的输入框选择器
                'textarea[placeholder*="输入"]',
                'textarea[placeholder*="问题"]',
                '.chat-input textarea',
                'input[type="text"]',
                '[contenteditable="true"]'
            ]

            input_element = None
            for selector in input_selectors:
                try:
                    input_element = self.page.locator(selector).last
                    if input_element.is_visible():
                        break
                except:
                    continue

            if not input_element:
                raise Exception("未找到输入框")

            # 点击输入框激活
            input_element.click()
            # 输入文本
            input_element.fill(textwrap.dedent(full_prompt))

            # 3. 发送消息 - 基于实际观察到的结构
            send_selectors = [
                '#yuanbao-send-btn',  # 腾讯元宝实际使用的发送按钮ID
                'button[aria-label="发送"]',
                'button:has-text("发送")',
                '.send-btn',
                '[data-testid="send"]'
            ]

            for selector in send_selectors:
                try:
                    send_btn = self.page.locator(selector)
                    if send_btn.is_visible() and send_btn.is_enabled():
                        send_btn.click()
                        logger.info("消息已发送")
                        break
                except:
                    continue
            else:
                # 如果没找到发送按钮，尝试按回车键
                input_element.press("Enter")
                logger.info("通过回车键发送消息")

            # 4. 等待回复完成
            self._wait_for_response_complete()

            # 5. 获取回复
            return self._get_reply()

        except Exception as e:
            logger.error(f"聊天过程中出错: {e}")
            raise

    def is_ready(self) -> bool:
        """检查平台是否准备好接收新消息"""
        try:
            # 检查输入框是否可见且可用 - 基于实际观察到的结构
            input_selectors = [
                '.ql-editor',  # 腾讯元宝实际使用的输入框选择器
                'textarea[placeholder*="输入"]',
                'textarea[placeholder*="问题"]',
                '.chat-input textarea',
                'input[type="text"]'
            ]

            for selector in input_selectors:
                try:
                    input_element = self.page.locator(selector).last
                    if input_element.is_visible() and input_element.is_enabled():
                        return True
                except:
                    continue

            return False

        except Exception:
            return False

    def save_chat(self, chat_name: str):
        """保存当前会话并命名"""
        try:
            logger.warning("腾讯元宝的会话保存功能待实现")
        except Exception as e:
            logger.error(f"保存会话失败: {e}")

    def del_chat(self, chat_name: str):
        """删除会话"""
        logger.warning("腾讯元宝的会话删除功能待实现")
