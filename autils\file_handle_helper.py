import logging
import re
from abc import ABC
from pathlib import Path

from autils.md_helper3 import MarkdownHelper
from autils.random_helper import generate_mysql_int_unique_id


class FileHandler(ABC):
    """抽象基类，定义文件处理程序的接口。"""
    def process_file_path(self, relative_file_path: str) -> str:
        """
        处理相对于in_dir的输入文件路径，并返回处理后的相对于out_dir的路径。
        默认实现不改变路径。
        """
        return relative_file_path

    def process_text_content(self, in_file_path: Path, content: str) -> str:
        """处理文本文件内容，并返回处理后的内容。默认实现不改变内容。"""
        return content
    
    def process_binary_content(self, in_file_path: Path, content: bytes) -> bytes:
        """处理二进制文件内容，并返回处理后的内容。默认实现不改变内容。"""
        return content


class RenameHandler(FileHandler):
    """将文件扩展名从.md改为.txt的处理器。"""
    def process_file_path(self, relative_file_path: str) -> str:
        # 使用pathlib更安全地处理路径和后缀
        p = Path(relative_file_path)
        if p.suffix == ".md":
            return str(p.with_suffix(".txt"))
        return relative_file_path


class ReplaceHandler(FileHandler):
    """将文件内容中的"old"替换为"new"的处理器。"""
    def process_text_content(self, in_file_path: Path, content: str) -> str:
        return content.replace("old", "new")


class CopyOnlyHandler(FileHandler):
    """一个只复制文件，不做任何修改的处理器。"""
    pass


class MarkdownIdUpdateHandler(FileHandler):
    """
    处理Markdown文件，更新或添加front matter中的id。
    非Markdown文件内容保持不变，相当于只复制。
    """
    def process_text_content(self, in_file_path: Path, content: str) -> str:
        # 现在FileHandler会根据文件类型自动分派，所以不需要再次检查后缀
        # 只有在确认为Markdown文件时才尝试处理，否则返回原始内容
        if in_file_path.suffix.lower() == '.md':
            try:
                # 修正：使用 from_content 传入已读取的内容
                md_helper = MarkdownHelper.from_content(content)
                front_matter = md_helper.extract_front_matter()

                if "id" in front_matter:
                    logging.info(f"File: {in_file_path.name}, Old ID: {front_matter['id']}")
                    front_matter["id"] = generate_mysql_int_unique_id()
                    logging.info(f"File: {in_file_path.name}, New ID: {front_matter['id']}")
                else:
                    front_matter["id"] = generate_mysql_int_unique_id()
                    logging.info(f"File: {in_file_path.name}, Added ID: {front_matter['id']}")

                md_helper.update_front_matter(front_matter)
                return md_helper.to_full_content_string()
            except Exception as e:
                logging.error(f"处理Markdown文件 {in_file_path} 失败: {e}")
                return content  # 发生错误时返回原始内容
        else:
            return content # 非markdown文件直接返回原始内容


class InPlaceFileHandler(FileHandler):
    """
    安全的原地文件更新处理器基类。
    使用临时文件确保数据安全，支持原子操作和错误回滚。
    """
    def __init__(self, backup_suffix=".backup", temp_suffix=".tmp"):
        self.backup_suffix = backup_suffix
        self.temp_suffix = temp_suffix

    def process_file_in_place(self, file_path: Path, handlers: list) -> bool:
        """
        安全地原地更新文件

        Args:
            file_path: 要处理的文件路径
            handlers: 处理器列表

        Returns:
            bool: 处理是否成功
        """
        import tempfile
        import shutil

        try:
            # 1. 创建临时文件
            temp_file = file_path.with_suffix(file_path.suffix + self.temp_suffix)
            backup_file = file_path.with_suffix(file_path.suffix + self.backup_suffix)

            # 2. 读取原文件内容
            try:
                original_content = file_path.read_text(encoding='utf-8')
                is_text = True
            except UnicodeDecodeError:
                original_content = file_path.read_bytes()
                is_text = False

            # 3. 处理内容
            processed_content = original_content
            for handler in handlers:
                try:
                    if is_text:
                        processed_content = handler.process_text_content(file_path, processed_content)
                    else:
                        processed_content = handler.process_binary_content(file_path, processed_content)
                except Exception as e:
                    logging.error(f"处理文件内容失败 (Handler: {handler.__class__.__name__}, File: {file_path}): {e}")
                    return False

            # 4. 检查内容是否有变化
            if processed_content == original_content:
                logging.info(f"文件内容无变化，跳过更新: {file_path}")
                return True

            # 5. 写入临时文件
            if is_text:
                temp_file.write_text(processed_content, encoding='utf-8')
            else:
                temp_file.write_bytes(processed_content)

            # # 6. 创建备份文件
            # if backup_file.exists():
            #     backup_file.unlink()
            # shutil.copy2(file_path, backup_file)

            # 7. 原子替换（Windows上使用move操作）
            if file_path.exists():
                file_path.unlink()
            temp_file.rename(file_path)

            logging.info(f"文件原地更新成功: {file_path}")
            return True

        except Exception as e:
            logging.error(f"原地更新文件失败 {file_path}: {e}")

            # 清理临时文件
            if temp_file.exists():
                temp_file.unlink()

            return False


class MarkdownToHtmlHandler(FileHandler):
    """
    将HTML文件中的Markdown语法转换为HTML语法的处理器。
    主要转换：
    - **粗体文本** → <strong>粗体文本</strong>
    - *斜体文本* → <em>斜体文本</em>
    - `代码` → <code>代码</code>
    - ~~删除线~~ → <del>删除线</del>
    - [链接文本](URL) → <a href="URL">链接文本</a>
    """
    def process_text_content(self, in_file_path: Path, content: str) -> str:
        # 只处理HTML文件
        try:
            converted_content = content
            conversion_count = 0

            # 1. 转换粗体语法：**text** → <strong>text</strong>
            # 使用非贪婪匹配，避免跨越多个粗体标记
            bold_pattern = r'\*\*([^*]+?)\*\*'
            new_content = re.sub(bold_pattern, r'<strong>\1</strong>', converted_content)
            if new_content != converted_content:
                conversion_count += len(re.findall(bold_pattern, converted_content))
                converted_content = new_content

            # 2. 转换斜体语法：*text* → <em>text</em>
            # 策略：只转换明确的斜体模式，避免数学公式
            # 规则：
            # - 必须包含字母或中文字符（不是纯数字或符号）
            # - 前后不能紧邻数字、字母、括号
            # - 不能包含数学运算符 =、+、-、/
            # - 长度至少2个字符
            def is_likely_italic(text):
                # 检查是否包含字母或中文
                if not re.search(r'[a-zA-Z\u4e00-\u9fff]', text):
                    return False
                # 检查是否包含数学符号
                if re.search(r'[=+\-/]', text):
                    return False
                # 检查长度
                if len(text.strip()) < 1:
                    return False
                # 排除单个字母（通常是数学变量）
                if re.match(r'^\s*[a-zA-Z]\s*$', text):
                    return False
                # 必须包含中文或者多个字母组成的单词
                if re.search(r'[\u4e00-\u9fff]', text) or re.search(r'[a-zA-Z]{2,}', text):
                    return True
                return False

            # 找到所有可能的斜体候选
            italic_candidates = re.findall(r'(?<![a-zA-Z0-9()])\*([^*\n]+?)\*(?![a-zA-Z0-9()])', converted_content)

            # 只转换符合条件的斜体
            for candidate in italic_candidates:
                if is_likely_italic(candidate):
                    pattern = r'(?<![a-zA-Z0-9()])\*(' + re.escape(candidate) + r')\*(?![a-zA-Z0-9()])'
                    new_content = re.sub(pattern, r'<em>\1</em>', converted_content, count=1)
                    if new_content != converted_content:
                        conversion_count += 1
                        converted_content = new_content

            # 3. 转换行内代码：`code` → <code>code</code>
            code_pattern = r'`([^`\n]+?)`'
            new_content = re.sub(code_pattern, r'<code>\1</code>', converted_content)
            if new_content != converted_content:
                conversion_count += len(re.findall(code_pattern, converted_content))
                converted_content = new_content

            # 4. 转换删除线：~~text~~ → <del>text</del>
            strikethrough_pattern = r'~~([^~\n]+?)~~'
            new_content = re.sub(strikethrough_pattern, r'<del>\1</del>', converted_content)
            if new_content != converted_content:
                conversion_count += len(re.findall(strikethrough_pattern, converted_content))
                converted_content = new_content

            # 5. 转换链接：[text](url) → <a href="url">text</a>
            # 注意：避免转换已经是HTML的链接
            link_pattern = r'(?<!href=")\[([^\]]+?)\]\(([^)]+?)\)(?!")'
            new_content = re.sub(link_pattern, r'<a href="\2">\1</a>', converted_content)
            if new_content != converted_content:
                conversion_count += len(re.findall(link_pattern, converted_content))
                converted_content = new_content

            if conversion_count > 0:
                logging.info(f"转换了文件中的{conversion_count}个Markdown语法: {in_file_path.name}")

            return converted_content

        except Exception as e:
            logging.error(f"转换Markdown语法失败 {in_file_path}: {e}")
            return content


class QuestionMarkdownToHtmlHandler(FileHandler):
    """
    将选择题YAML文件中的Markdown语法转换为HTML语法的处理器。
    主要处理analysis等字段中的markdown内容，支持：
    - **粗体文本** → <strong>粗体文本</strong>
    - *斜体文本* → <em>斜体文本</em>
    - `代码` → <code>代码</code>
    - ~~删除线~~ → <del>删除线</del>
    - [链接文本](URL) → <a href="URL">链接文本</a>
    - * 列表项 → <li>列表项</li> (包裹在<ul>中)
    - 段落换行 → <p></p>标签包裹
    """

    def process_text_content(self, in_file_path: Path, content: str) -> str:
        """处理选择题YAML文件中的markdown语法转换"""
        try:
            # 只处理.md文件
            if in_file_path.suffix.lower() != '.md':
                return content

            # 使用MarkdownHelper解析文件
            from autils.md_helper3 import MarkdownHelper
            from olymat.utils.yaml_helper import load_yaml_str
            import yaml

            helper = MarkdownHelper.from_file(in_file_path)
            front_matter = helper.extract_front_matter()
            content_part = helper.extract_content()

            # 使用load_yaml_str解析content部分
            question_data = load_yaml_str(content_part)
            if not question_data:
                logging.warning(f"无法解析YAML内容: {in_file_path.name}")
                return content

            conversion_count = 0

            # 处理analysis字段
            if 'analysis' in question_data and question_data['analysis']:
                original_analysis = str(question_data['analysis'])
                # 清理错误的YAML字面量块标量指示符
                cleaned_analysis = original_analysis
                converted_analysis = self._convert_markdown_to_html(cleaned_analysis)

                if converted_analysis != original_analysis:
                    question_data['analysis'] = converted_analysis
                    conversion_count += 1

            # 处理title字段（可能包含markdown）
            if 'title' in question_data and question_data['title']:
                original_title = str(question_data['title'])
                # 清理错误的YAML字面量块标量指示符
                cleaned_title = original_title
                converted_title = self._convert_markdown_to_html(cleaned_title)

                if converted_title != original_title:
                    question_data['title'] = converted_title
                    conversion_count += 1

            # 处理sub_questions中的analysis字段
            if 'sub_questions' in question_data:
                for sub_q in question_data['sub_questions']:
                    if 'analysis' in sub_q and sub_q['analysis']:
                        original_sub_analysis = str(sub_q['analysis'])
                        cleaned_sub_analysis = original_sub_analysis
                        converted_sub_analysis = self._convert_markdown_to_html(cleaned_sub_analysis)

                        if converted_sub_analysis != original_sub_analysis:
                            sub_q['analysis'] = converted_sub_analysis
                            conversion_count += 1

            if conversion_count > 0:
                logging.info(f"转换了选择题中的{conversion_count}个字段的Markdown语法: {in_file_path.name}")

                # 重新组装文件内容
                from test_20_jkt_ops.comm.util import LiteralStr, wrap_multiline, literal_str_representer
                yaml.add_representer(LiteralStr, literal_str_representer)

                wrapped_data = wrap_multiline(question_data)
                new_content_part = yaml.dump(wrapped_data, allow_unicode=True, sort_keys=False)

                # 更新helper的内容
                helper._body_content = new_content_part

                # 返回完整的文件内容
                return helper.to_full_content_string()
            else:
                # 没有变化，返回原内容
                return content

        except Exception as e:
            logging.error(f"转换选择题Markdown语法失败 {in_file_path}: {e}")
            return content

    def _convert_markdown_to_html(self, text: str) -> str:
        """将markdown语法转换为HTML语法"""
        if not text or not isinstance(text, str):
            return text

        converted_text = text

        # 1. 转换粗体语法：**text** → <strong>text</strong>
        bold_pattern = r'\*\*([^*]+?)\*\*'
        converted_text = re.sub(bold_pattern, r'<strong>\1</strong>', converted_text)

        # 2. 转换斜体语法：*text* → <em>text</em>
        # 注意：避免与粗体语法冲突，避免匹配数学表达式中的乘号
        # 使用简单但有效的方法：要求前后是空格或标点，且内容不是单个字符
        italic_pattern = r'(?<!\*)\*([a-zA-Z\u4e00-\u9fff][^*\n]*?[a-zA-Z\u4e00-\u9fff\s]|[\u4e00-\u9fff]+)\*(?!\*)'
        # 进一步过滤：排除明显的数学表达式模式
        def italic_replacer(match):
            content = match.group(1)
            # 如果内容看起来像数学变量（单个字母+空格+单个字母），则不转换
            if re.match(r'^[a-zA-Z]\s*[+\-*/]\s*[a-zA-Z]', content):
                return match.group(0)  # 返回原始匹配
            return f'<em>{content}</em>'

        converted_text = re.sub(italic_pattern, italic_replacer, converted_text)

        # 3. 转换行内代码：`text` → <code>text</code>
        code_pattern = r'`([^`\n]+?)`'
        converted_text = re.sub(code_pattern, r'<code>\1</code>', converted_text)

        # 4. 转换删除线：~~text~~ → <del>text</del>
        strikethrough_pattern = r'~~([^~\n]+?)~~'
        converted_text = re.sub(strikethrough_pattern, r'<del>\1</del>', converted_text)

        # 5. 转换链接：[text](url) → <a href="url">text</a>
        link_pattern = r'(?<!href=")\[([^\]]+?)\]\(([^)]+?)\)(?!")'
        converted_text = re.sub(link_pattern, r'<a href="\2">\1</a>', converted_text)

        # 6. 转换列表项：处理以 * 或 - 开头的行
        converted_text = self._convert_lists(converted_text)

        # 7. 转换段落：处理换行为<p>标签
        converted_text = self._convert_paragraphs(converted_text)

        return converted_text

    def _convert_lists(self, text: str) -> str:
        """转换列表项为HTML格式"""
        lines = text.split('\n')
        result_lines = []
        in_list = False

        for line in lines:
            stripped_line = line.strip()

            # 检查是否为列表项（以 * 或 - 开头，后面跟空格）
            if re.match(r'^[*-]\s+', stripped_line):
                if not in_list:
                    result_lines.append('<ul>')
                    in_list = True

                # 提取列表项内容（去掉 * 或 - 和空格）
                list_content = re.sub(r'^[*-]\s+', '', stripped_line)
                result_lines.append(f'<li>{list_content}</li>')
            else:
                if in_list:
                    result_lines.append('</ul>')
                    in_list = False
                result_lines.append(line)

        # 如果文本结束时还在列表中，需要关闭列表
        if in_list:
            result_lines.append('</ul>')

        return '\n'.join(result_lines)

    def _convert_paragraphs(self, text: str) -> str:
        """转换段落为HTML格式，每个非空行都包裹在独立的<p>标签中"""
        lines = text.split('\n')
        result_lines = []

        for line in lines:
            stripped_line = line.strip()

            # 如果是空行，保持空行
            if not stripped_line:
                result_lines.append('')
            # 如果行已经包含HTML标签（包括<p>、<ul>、<li>等），直接保留
            elif self._contains_html_tags(stripped_line):
                result_lines.append(line)
            else:
                # 将每个非空的纯文本行包裹在<p>标签中
                result_lines.append(f'<p>{stripped_line}</p>')

        return '\n'.join(result_lines)

    def _contains_html_tags(self, text: str) -> bool:
        """检查文本是否包含HTML标签"""
        import re
        # 检查是否包含HTML标签（包括开始标签、结束标签、自闭合标签）
        html_pattern = r'<[^>]+>'
        return bool(re.search(html_pattern, text))

